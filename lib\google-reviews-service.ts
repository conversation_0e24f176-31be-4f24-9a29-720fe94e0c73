import { supabase } from "./supabase";

// Types for Google Business Profile Reviews
export interface GoogleBusinessReview {
	name: string;
	reviewId: string;
	reviewer: {
		profilePhotoUrl?: string;
		displayName: string;
	};
	starRating: "ONE" | "TWO" | "THREE" | "FOUR" | "FIVE";
	comment?: string;
	createTime: string;
	updateTime: string;
}

export interface GoogleBusinessResponse {
	reviews: GoogleBusinessReview[];
	nextPageToken?: string;
	totalSize: number;
}

export interface CachedReview {
	id: string;
	author_name: string;
	author_initials: string;
	rating: number;
	text: string;
	date: string;
	relative_time: string;
	profile_photo_url?: string;
	source: "google_places" | "google_business" | "manual";
	is_active: boolean;
	created_at: string;
	updated_at: string;
}

export interface ReviewsServiceConfig {
	googleBusinessClientId?: string;
	googleBusinessClientSecret?: string;
	googleBusinessAccountId?: string;
	googleBusinessLocationId?: string;
	fallbackToCache: boolean;
	maxCacheAge: number; // in hours
	minRating?: number;
	maxReviews?: number;
}

class GoogleReviewsService {
	private config: ReviewsServiceConfig;

	constructor(config: ReviewsServiceConfig) {
		this.config = {
			maxReviews: 10,
			...config,
			fallbackToCache: config.fallbackToCache ?? true,
			maxCacheAge: config.maxCacheAge ?? 24, // 24 hours default
		};
	}

	/**
	 * Fetch reviews from Google Business Profile API
	 */
	async fetchFromGoogleBusiness(): Promise<GoogleBusinessReview[]> {
		if (!this.config.googleBusinessAccountId || !this.config.googleBusinessLocationId) {
			throw new Error("Google Business Profile account ID and location ID are required");
		}

		// Note: This is a simplified implementation
		// In production, you'll need to implement OAuth 2.0 flow for authentication
		const locationName = `accounts/${this.config.googleBusinessAccountId}/locations/${this.config.googleBusinessLocationId}`;
		const url = `https://mybusiness.googleapis.com/v4/${locationName}/reviews`;

		try {
			// This would need proper OAuth 2.0 authentication in production
			const response = await fetch(url, {
				headers: {
					Authorization: `Bearer ${await this.getAccessToken()}`,
					"Content-Type": "application/json",
				},
			});

			if (!response.ok) {
				throw new Error(`Google Business Profile API error: ${response.status} - ${response.statusText}`);
			}

			const data: GoogleBusinessResponse = await response.json();
			return data.reviews || [];
		} catch (error) {
			console.error("Error fetching from Google Business Profile API:", error);
			throw error;
		}
	}

	/**
	 * Get OAuth 2.0 access token (simplified - needs proper implementation)
	 */
	private async getAccessToken(): Promise<string> {
		// This is a placeholder - in production you need to implement:
		// 1. OAuth 2.0 flow for initial authorization
		// 2. Token refresh mechanism
		// 3. Secure token storage
		throw new Error("OAuth 2.0 authentication not implemented. Please set up proper authentication flow.");
	}

	/**
	 * Transform Google Business Profile review to our cached format
	 */
	private transformGoogleBusinessReview(
		review: GoogleBusinessReview
	): Omit<CachedReview, "id" | "created_at" | "updated_at"> {
		// Generate initials from author name
		const initials = review.reviewer.displayName
			.split(" ")
			.map((name) => name.charAt(0).toUpperCase())
			.slice(0, 2)
			.join("");

		// Convert star rating to number
		const ratingMap = { ONE: 1, TWO: 2, THREE: 3, FOUR: 4, FIVE: 5 };
		const rating = ratingMap[review.starRating];

		// Calculate relative time
		const reviewDate = new Date(review.createTime);
		const now = new Date();
		const diffTime = Math.abs(now.getTime() - reviewDate.getTime());
		const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
		const relativeTime = diffDays === 1 ? "il y a 1 jour" : `il y a ${diffDays} jours`;

		return {
			author_name: review.reviewer.displayName,
			author_initials: initials,
			rating: rating,
			text: review.comment || "",
			date: review.createTime,
			relative_time: relativeTime,
			profile_photo_url: review.reviewer.profilePhotoUrl,
			source: "google_business",
			is_active: true,
		};
	}

	/**
	 * Cache reviews in database
	 */
	async cacheReviews(reviews: GoogleBusinessReview[]): Promise<void> {
		if (!reviews.length) return;

		const transformedReviews = reviews.map((review) => ({
			...this.transformGoogleBusinessReview(review),
			external_id: `google_business_${review.reviewId}`,
		}));

		try {
			// Use upsert to avoid duplicates
			const { error } = await supabase.from("cached_reviews").upsert(transformedReviews, {
				onConflict: "external_id",
				ignoreDuplicates: false,
			});

			if (error) {
				console.error("Error caching reviews:", error);
				throw error;
			}

			console.log(`Successfully cached ${transformedReviews.length} reviews`);
		} catch (error) {
			console.error("Error caching reviews:", error);
			throw error;
		}
	}

	/**
	 * Get cached reviews from database
	 */
	async getCachedReviews(): Promise<CachedReview[]> {
		try {
			let query = supabase
				.from("cached_reviews")
				.select("*")
				.eq("is_active", true)
				.order("date", { ascending: false });

			// Apply rating filter if specified
			if (this.config.minRating) {
				query = query.gte("rating", this.config.minRating);
			}

			// Apply limit if specified
			if (this.config.maxReviews) {
				query = query.limit(this.config.maxReviews);
			}

			const { data, error } = await query;

			if (error) {
				console.error("Error fetching cached reviews:", error);
				throw error;
			}

			return data || [];
		} catch (error) {
			console.error("Error getting cached reviews:", error);
			throw error;
		}
	}

	/**
	 * Check if cached reviews are still fresh
	 */
	async isCacheFresh(): Promise<boolean> {
		try {
			const { data, error } = await supabase
				.from("cached_reviews")
				.select("updated_at")
				.order("updated_at", { ascending: false })
				.limit(1);

			if (error || !data || data.length === 0) {
				return false;
			}

			const lastUpdate = new Date(data[0].updated_at);
			const now = new Date();
			const hoursSinceUpdate = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60);

			return hoursSinceUpdate < this.config.maxCacheAge;
		} catch (error) {
			console.error("Error checking cache freshness:", error);
			return false;
		}
	}

	/**
	 * Get reviews with fallback strategy
	 */
	async getReviews(): Promise<CachedReview[]> {
		try {
			// First, try to get fresh reviews from Google Business Profile API
			if (this.config.googleBusinessAccountId && this.config.googleBusinessLocationId) {
				try {
					const googleReviews = await this.fetchFromGoogleBusiness();

					if (googleReviews.length > 0) {
						// Cache the fresh reviews
						await this.cacheReviews(googleReviews);

						// Return the cached reviews (which now include the fresh ones)
						return await this.getCachedReviews();
					}
				} catch (error) {
					console.warn("Failed to fetch from Google Business Profile API, falling back to cache:", error);
				}
			}

			// Fallback to cached reviews
			if (this.config.fallbackToCache) {
				const cachedReviews = await this.getCachedReviews();

				if (cachedReviews.length > 0) {
					console.log("Using cached reviews as fallback");
					return cachedReviews;
				}
			}

			// If no cached reviews available, return empty array
			console.warn("No reviews available from any source");
			return [];
		} catch (error) {
			console.error("Error in getReviews:", error);

			// Last resort: try to get any cached reviews
			if (this.config.fallbackToCache) {
				try {
					return await this.getCachedReviews();
				} catch (cacheError) {
					console.error("Even cached reviews failed:", cacheError);
				}
			}

			return [];
		}
	}

	/**
	 * Refresh reviews manually
	 */
	async refreshReviews(): Promise<{ success: boolean; count: number; error?: string }> {
		try {
			if (!this.config.googleBusinessAccountId || !this.config.googleBusinessLocationId) {
				throw new Error("Google Business Profile API configuration missing");
			}

			const googleReviews = await this.fetchFromGoogleBusiness();
			await this.cacheReviews(googleReviews);

			return {
				success: true,
				count: googleReviews.length,
			};
		} catch (error) {
			console.error("Error refreshing reviews:", error);
			return {
				success: false,
				count: 0,
				error: error instanceof Error ? error.message : "Unknown error",
			};
		}
	}
}

// Export singleton instance with safe configuration
export const googleReviewsService = new GoogleReviewsService({
	googleBusinessClientId: process.env.GOOGLE_BUSINESS_PROFILE_CLIENT_ID || undefined,
	googleBusinessClientSecret: process.env.GOOGLE_BUSINESS_PROFILE_CLIENT_SECRET || undefined,
	googleBusinessAccountId: process.env.GOOGLE_BUSINESS_ACCOUNT_ID || undefined,
	googleBusinessLocationId: process.env.GOOGLE_BUSINESS_LOCATION_ID || undefined,
	fallbackToCache: true,
	maxCacheAge: 24,
	minRating: 4, // Only show 4+ star reviews
	maxReviews: 10,
});

export default GoogleReviewsService;
