"use client";

import AdminLayout from "@/components/admin/AdminLayout";
import { useSettings } from "@/lib/use-settings";
import { Info, Loader2, Save } from "lucide-react";
import { useState } from "react";

export const dynamic = "force-dynamic";

export default function AdminSettingsPage() {
	const { categorized, loading, error, updateSetting } = useSettings();
	const [saving, setSaving] = useState<string | null>(null);
	const [localValues, setLocalValues] = useState<Record<string, any>>({});

	const handleValueChange = (key: string, value: any) => {
		setLocalValues((prev) => ({ ...prev, [key]: value }));
	};

	const handleSave = async (key: string, value: any) => {
		setSaving(key);
		try {
			const success = await updateSetting(key, value);
			if (success) {
				// Remove from local values since it's now saved
				setLocalValues((prev) => {
					const newValues = { ...prev };
					delete newValues[key];
					return newValues;
				});
			}
		} finally {
			setSaving(null);
		}
	};

	const getValue = (key: string, originalValue: any) => {
		return localValues.hasOwnProperty(key) ? localValues[key] : originalValue;
	};

	const hasChanges = (key: string) => {
		return localValues.hasOwnProperty(key);
	};

	if (loading) {
		return (
			<AdminLayout adminOnly>
				<div className="flex items-center justify-center h-64">
					<div className="flex items-center space-x-2">
						<Loader2 className="h-6 w-6 animate-spin text-blue-600" />
						<span className="text-gray-600">Chargement des paramètres...</span>
					</div>
				</div>
			</AdminLayout>
		);
	}

	if (error) {
		return (
			<AdminLayout adminOnly>
				<div className="bg-red-50 border border-red-200 rounded-md p-4">
					<div className="flex">
						<Info className="h-5 w-5 text-red-400" />
						<div className="ml-3">
							<h3 className="text-sm font-medium text-red-800">Erreur</h3>
							<div className="mt-2 text-sm text-red-700">{error}</div>
						</div>
					</div>
				</div>
			</AdminLayout>
		);
	}

	const getCategoryTitle = (category: string) => {
		const titles: Record<string, string> = {
			business: "Informations de l'entreprise",
			booking: "Réservations",
			payment: "Paiements",
			notification: "Notifications",
			system: "Système",
			security: "Sécurité",
			general: "Général",
		};
		return titles[category] || category.replace("_", " ");
	};

	const getSettingLabel = (key: string) => {
		const labels: Record<string, string> = {
			business_name: "Nom de l'entreprise",
			business_email: "Email de contact",
			business_phone: "Téléphone",
			business_address: "Adresse",
			booking_advance_days: "Réservation à l'avance (jours)",
			booking_cancellation_hours: "Annulation (heures à l'avance)",
			payment_currency: "Devise",
			payment_methods: "Méthodes de paiement acceptées",
			operating_days: "Jours d'ouverture",
			notification_email: "Notifications par email",
			notification_sms: "Notifications par SMS",
			business_hours: "Horaires d'ouverture",
			max_advance_booking: "Réservation maximum à l'avance",
			min_advance_booking: "Réservation minimum à l'avance",
			admin_notification_email: "Email des notifications admin",
			facebook_url: "URL Facebook",
			instagram_url: "URL Instagram",
			tiktok_url: "URL TikTok",
			tripadvisor_url: "URL TripAdvisor",
		};
		return labels[key] || key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
	};

	return (
		<AdminLayout adminOnly>
			<div className="p-6">
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Paramètres</h1>
					<p className="text-gray-600">Configurez les paramètres de votre entreprise</p>
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{Object.entries(categorized).map(([category, settings]) => (
						<div key={category} className="bg-white rounded-xl shadow-sm border border-gray-200">
							<div className="p-6 border-b border-gray-200">
								<h2 className="text-xl font-semibold text-gray-900">{getCategoryTitle(category)}</h2>
							</div>
							<div className="p-6 space-y-6">
								{Object.entries(settings).map(([key, setting]) => (
									<div key={key} className="space-y-2">
										<div className="flex items-center justify-between">
											<div className="flex-1">
												<label className="block text-sm font-medium text-gray-700">
													{getSettingLabel(key)}
												</label>
												{setting.description && (
													<p className="text-xs text-gray-500 mt-1">{setting.description}</p>
												)}
											</div>
											{hasChanges(key) && (
												<button
													onClick={() => handleSave(key, localValues[key])}
													disabled={saving === key}
													className="ml-3 inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-emerald-600 hover:bg-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 disabled:opacity-50"
												>
													{saving === key ? (
														<Loader2 className="h-3 w-3 animate-spin" />
													) : (
														<Save className="h-3 w-3" />
													)}
													<span className="ml-1">Sauvegarder</span>
												</button>
											)}
										</div>

										<div className="mt-2">
											{setting.type === "boolean" ? (
												<select
													value={getValue(key, setting.value).toString()}
													onChange={(e) => handleValueChange(key, e.target.value === "true")}
													className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
												>
													<option value="true">Activé</option>
													<option value="false">Désactivé</option>
												</select>
											) : key === "operating_days" ? (
												<div className="space-y-2">
													{[
														{ key: "monday", label: "Lundi" },
														{ key: "tuesday", label: "Mardi" },
														{ key: "wednesday", label: "Mercredi" },
														{ key: "thursday", label: "Jeudi" },
														{ key: "friday", label: "Vendredi" },
														{ key: "saturday", label: "Samedi" },
														{ key: "sunday", label: "Dimanche" },
													].map((day) => {
														const currentValue = getValue(key, setting.value) || [];
														const isChecked = currentValue.includes(day.key);
														return (
															<label key={day.key} className="flex items-center">
																<input
																	type="checkbox"
																	checked={isChecked}
																	onChange={(e) => {
																		const newValue = e.target.checked
																			? [...currentValue, day.key]
																			: currentValue.filter(
																					(d: string) => d !== day.key
																				);
																		handleValueChange(key, newValue);
																	}}
																	className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
																/>
																<span className="ml-2 text-sm text-gray-700">
																	{day.label}
																</span>
															</label>
														);
													})}
												</div>
											) : key === "payment_methods" ? (
												<div className="space-y-2">
													{[
														{ key: "cash", label: "Espèces" },
														{ key: "card", label: "Carte bancaire (TPE)" },
														{ key: "bank_transfer", label: "Virement bancaire" },
														{ key: "check", label: "Chèque" },
														{ key: "stripe", label: "Stripe" },
														{ key: "paypal", label: "PayPal" },
													].map((method) => {
														const currentValue = getValue(key, setting.value) || [];
														const isChecked = currentValue.includes(method.key);
														return (
															<label key={method.key} className="flex items-center">
																<input
																	type="checkbox"
																	checked={isChecked}
																	onChange={(e) => {
																		const newValue = e.target.checked
																			? [...currentValue, method.key]
																			: currentValue.filter(
																					(m: string) => m !== method.key
																				);
																		handleValueChange(key, newValue);
																	}}
																	className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
																/>
																<span className="ml-2 text-sm text-gray-700">
																	{method.label}
																</span>
															</label>
														);
													})}
												</div>
											) : setting.type === "json" ? (
												<textarea
													value={JSON.stringify(getValue(key, setting.value), null, 2)}
													onChange={(e) => {
														try {
															const parsed = JSON.parse(e.target.value);
															handleValueChange(key, parsed);
														} catch {
															handleValueChange(key, e.target.value);
														}
													}}
													rows={3}
													className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm font-mono"
												/>
											) : setting.type === "number" ? (
												<input
													type="number"
													value={getValue(key, setting.value)}
													onChange={(e) =>
														handleValueChange(key, parseFloat(e.target.value) || 0)
													}
													className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
												/>
											) : key.includes("email") ? (
												<input
													type="email"
													value={getValue(key, setting.value)}
													onChange={(e) => handleValueChange(key, e.target.value)}
													className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
													placeholder="<EMAIL>"
												/>
											) : (
												<input
													type="text"
													value={getValue(key, setting.value)}
													onChange={(e) => handleValueChange(key, e.target.value)}
													className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
												/>
											)}
										</div>
									</div>
								))}
							</div>
						</div>
					))}
				</div>
			</div>
		</AdminLayout>
	);
}
