"use client";

import { adminApi } from "@/lib/api-client";
import { DiscountCoupon, DiscountCouponInsert } from "@/lib/types";
import { AlertCircle, Edit, Filter, Loader2, Percent, Plus, Save, Search, Tag, Trash2, X } from "lucide-react";
import { useEffect, useState } from "react";
import Button from "./ui/Button";

const AdminDiscountCoupons = () => {
	const [coupons, setCoupons] = useState<DiscountCoupon[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [isCreating, setIsCreating] = useState(false);
	const [editingId, setEditingId] = useState<string | null>(null);
	const [editForm, setEditForm] = useState<Partial<DiscountCouponInsert>>({});
	const [saving, setSaving] = useState(false);

	// Search and filter state
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState<"all" | "active" | "inactive">("all");
	const [typeFilter, setTypeFilter] = useState<"all" | "percentage" | "fixed">("all");
	const [selectedCoupons, setSelectedCoupons] = useState<string[]>([]);

	// Services state
	const [services, setServices] = useState<Array<{ id: string; name: string; category: string }>>([]);

	// Form validation state
	const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
	const [isFormValid, setIsFormValid] = useState(true);

	useEffect(() => {
		fetchCoupons();
		fetchServices();
	}, []);

	const fetchCoupons = async () => {
		try {
			setLoading(true);
			setError(null);
			const response = await adminApi.getDiscountCoupons();
			if (response?.discountCoupons) {
				setCoupons(response.discountCoupons);
			}
		} catch (err) {
			console.error("Error fetching discount coupons:", err);
			setError("Erreur lors du chargement des codes de réduction");
		} finally {
			setLoading(false);
		}
	};

	const fetchServices = async () => {
		try {
			const response = await adminApi.getServices();
			if (response?.services) {
				setServices(
					response.services.map((service: any) => ({
						id: service.id,
						name: service.name,
						category: service.category,
					}))
				);
			}
		} catch (err) {
			console.error("Error fetching services:", err);
			// Don't show error for services as it's not critical
		}
	};

	const handleCreate = () => {
		setIsCreating(true);
		setEditForm({
			code: "",
			description: "",
			discount_type: "percentage",
			discount_value: 0,
			min_purchase_amount: undefined,
			max_discount_amount: undefined,
			usage_limit: undefined,
			valid_from: "",
			valid_until: "",
			applicable_services: [],
			is_active: true,
		});
		setError(null);
		setFieldErrors({});
		setIsFormValid(false); // Start with invalid form since required fields are empty
	};

	const handleEdit = (coupon: DiscountCoupon) => {
		setEditingId(coupon.id);
		const formData = {
			code: coupon.code,
			description: coupon.description || "",
			discount_type: coupon.discount_type,
			discount_value: coupon.discount_value,
			min_purchase_amount: coupon.min_purchase_amount || undefined,
			max_discount_amount: coupon.max_discount_amount || undefined,
			usage_limit: coupon.usage_limit || undefined,
			valid_from: coupon.valid_from ? coupon.valid_from.split("T")[0] : "",
			valid_until: coupon.valid_until ? coupon.valid_until.split("T")[0] : "",
			applicable_services: coupon.applicable_services || [],
			is_active: coupon.is_active,
		};
		setEditForm(formData);

		// Validate the form data
		const errors = validateForm(formData);
		setFieldErrors(errors);
		setIsFormValid(Object.keys(errors).length === 0);
		setError(null);
	};

	const handleSave = async () => {
		// Validate the entire form
		const errors = validateForm(editForm);
		setFieldErrors(errors);
		setIsFormValid(Object.keys(errors).length === 0);

		if (Object.keys(errors).length > 0) {
			setError("Veuillez corriger les erreurs dans le formulaire avant de sauvegarder");
			return;
		}

		try {
			setSaving(true);
			setError(null);

			const formData = {
				...editForm,
				valid_from: editForm.valid_from ? new Date(editForm.valid_from).toISOString() : null,
				valid_until: editForm.valid_until ? new Date(editForm.valid_until).toISOString() : null,
			};

			if (isCreating) {
				await adminApi.createDiscountCoupon(formData);
				setIsCreating(false);
			} else if (editingId) {
				await adminApi.updateDiscountCoupon(editingId, formData);
				setEditingId(null);
			}

			setEditForm({});
			await fetchCoupons(); // Refresh the list
		} catch (err) {
			console.error("Error saving discount coupon:", err);
			setError("Erreur lors de la sauvegarde du code de réduction");
		} finally {
			setSaving(false);
		}
	};

	const handleCancel = () => {
		setIsCreating(false);
		setEditingId(null);
		setEditForm({});
		setError(null);
		setFieldErrors({});
		setIsFormValid(true);
	};

	const handleDelete = async (couponId: string) => {
		if (
			window.confirm(
				"Êtes-vous sûr de vouloir supprimer définitivement ce code de réduction ? Cette action est irréversible."
			)
		) {
			try {
				await adminApi.deleteDiscountCoupon(couponId);
				await fetchCoupons(); // Refresh the list
			} catch (err) {
				console.error("Error deleting discount coupon:", err);
				setError("Erreur lors de la suppression du code de réduction");
			}
		}
	};

	// Validation functions
	const validateField = (field: keyof DiscountCouponInsert, value: any): string => {
		switch (field) {
			case "code":
				if (!value || typeof value !== "string" || value.trim() === "") {
					return "Le code de réduction est obligatoire";
				}
				if (!/^[A-Z0-9]+$/.test(value.trim())) {
					return "Le code de réduction ne peut contenir que des lettres et des chiffres";
				}
				if (value.trim().length < 3) {
					return "Le code de réduction doit contenir au moins 3 caractères";
				}
				if (value.trim().length > 20) {
					return "Le code de réduction ne peut pas dépasser 20 caractères";
				}
				break;

			case "discount_value":
				if (value === null || value === undefined || value === "") {
					return "La valeur de réduction est obligatoire";
				}
				const numValue = Number(value);
				if (isNaN(numValue)) {
					return "La valeur de réduction doit être un nombre";
				}
				if (numValue <= 0) {
					return "La valeur de réduction doit être un nombre positif";
				}
				if (editForm.discount_type === "percentage" && numValue > 100) {
					return "Le pourcentage ne peut pas dépasser 100%";
				}
				if (numValue > 999999) {
					return "La valeur de réduction est trop élevée";
				}
				break;

			case "usage_limit":
				if (value !== null && value !== undefined && value !== "") {
					const numValue = Number(value);
					if (isNaN(numValue)) {
						return "La limite d'utilisation doit être un nombre";
					}
					if (numValue <= 0) {
						return "La limite d'utilisation doit être un nombre positif";
					}
					if (!Number.isInteger(numValue)) {
						return "La limite d'utilisation doit être un nombre entier";
					}
					if (numValue > 999999) {
						return "La limite d'utilisation est trop élevée";
					}
				}
				break;

			case "min_purchase_amount":
				if (value !== null && value !== undefined && value !== "") {
					const numValue = Number(value);
					if (isNaN(numValue)) {
						return "Le montant minimum d'achat doit être un nombre";
					}
					if (numValue < 0) {
						return "Le montant minimum d'achat ne peut pas être négatif";
					}
					if (numValue > 999999) {
						return "Le montant minimum d'achat est trop élevé";
					}
				}
				break;

			case "max_discount_amount":
				if (value !== null && value !== undefined && value !== "") {
					const numValue = Number(value);
					if (isNaN(numValue)) {
						return "La réduction maximum doit être un nombre";
					}
					if (numValue <= 0) {
						return "La réduction maximum doit être un nombre positif";
					}
					if (numValue > 999999) {
						return "La réduction maximum est trop élevée";
					}
				}
				break;

			case "valid_from":
			case "valid_until":
				if (value && editForm.valid_from && editForm.valid_until) {
					const fromDate = new Date(editForm.valid_from);
					const untilDate = new Date(editForm.valid_until);
					if (fromDate >= untilDate) {
						return "La date de fin doit être postérieure à la date de début";
					}
				}
				break;
		}
		return "";
	};

	const validateForm = (formData: Partial<DiscountCouponInsert>): Record<string, string> => {
		const errors: Record<string, string> = {};

		// Validate all fields
		Object.keys(formData).forEach((key) => {
			const field = key as keyof DiscountCouponInsert;
			const error = validateField(field, formData[field]);
			if (error) {
				errors[field] = error;
			}
		});

		// Cross-field validation for dates
		if (formData.valid_from && formData.valid_until) {
			const fromDate = new Date(formData.valid_from);
			const untilDate = new Date(formData.valid_until);
			if (fromDate >= untilDate) {
				errors.valid_until = "La date de fin doit être postérieure à la date de début";
			}
		}

		return errors;
	};

	const handleInputChange = (field: keyof DiscountCouponInsert, value: any) => {
		// Update form data
		const newFormData = { ...editForm, [field]: value };
		setEditForm(newFormData);

		// Validate the specific field
		const fieldError = validateField(field, value);
		const newFieldErrors = { ...fieldErrors };

		if (fieldError) {
			newFieldErrors[field] = fieldError;
		} else {
			delete newFieldErrors[field];
		}

		// Re-validate cross-field dependencies
		if (field === "valid_from" || field === "valid_until") {
			const dateError = validateField("valid_until", newFormData.valid_until);
			if (dateError) {
				newFieldErrors.valid_until = dateError;
			} else {
				delete newFieldErrors.valid_until;
			}
		}

		// Re-validate discount value when type changes
		if (field === "discount_type") {
			const discountError = validateField("discount_value", newFormData.discount_value);
			if (discountError) {
				newFieldErrors.discount_value = discountError;
			} else {
				delete newFieldErrors.discount_value;
			}
		}

		setFieldErrors(newFieldErrors);
		setIsFormValid(Object.keys(newFieldErrors).length === 0);
	};

	// Helper function to get field styling based on validation state
	const getFieldClassName = (field: keyof DiscountCouponInsert, baseClassName: string): string => {
		const hasError = fieldErrors[field];
		if (hasError) {
			return `${baseClassName} border-red-300 focus:ring-red-500 focus:border-red-500`;
		}
		return baseClassName;
	};

	// Filter coupons based on search and filters
	const filteredCoupons = coupons.filter((coupon) => {
		// Search filter
		if (searchTerm) {
			const searchLower = searchTerm.toLowerCase();
			if (
				!coupon.code.toLowerCase().includes(searchLower) &&
				!coupon.description?.toLowerCase().includes(searchLower)
			) {
				return false;
			}
		}

		// Status filter
		if (statusFilter === "active" && !coupon.is_active) return false;
		if (statusFilter === "inactive" && coupon.is_active) return false;

		// Type filter
		if (typeFilter !== "all" && coupon.discount_type !== typeFilter) return false;

		return true;
	});

	// Bulk operations
	const handleSelectAll = () => {
		if (selectedCoupons.length === filteredCoupons.length) {
			setSelectedCoupons([]);
		} else {
			setSelectedCoupons(filteredCoupons.map((c) => c.id));
		}
	};

	const handleSelectCoupon = (couponId: string) => {
		setSelectedCoupons((prev) =>
			prev.includes(couponId) ? prev.filter((id) => id !== couponId) : [...prev, couponId]
		);
	};

	const handleBulkDelete = async () => {
		if (selectedCoupons.length === 0) return;

		if (
			window.confirm(
				`Êtes-vous sûr de vouloir supprimer définitivement ${selectedCoupons.length} code(s) de réduction ? Cette action est irréversible.`
			)
		) {
			try {
				await Promise.all(selectedCoupons.map((id) => adminApi.deleteDiscountCoupon(id)));
				setSelectedCoupons([]);
				await fetchCoupons();
			} catch (err) {
				console.error("Error bulk deleting coupons:", err);
				setError("Erreur lors de la suppression des codes");
			}
		}
	};

	const formatCurrency = (amount: number) => {
		return new Intl.NumberFormat("fr-FR", {
			style: "currency",
			currency: "EUR",
		}).format(amount);
	};

	const formatDate = (dateString: string | null) => {
		if (!dateString) return "Aucune limite";
		// Parse date manually to avoid timezone issues
		const [year, month, day] = dateString.split("-").map(Number);
		const dateObj = new Date(year, month - 1, day);
		return dateObj.toLocaleDateString("fr-FR");
	};

	const getDiscountText = (type: string, value: number) => {
		if (type === "percentage") {
			return `${value}%`;
		} else {
			return formatCurrency(value);
		}
	};

	const isExpired = (validUntil: string | null) => {
		if (!validUntil) return false;
		// Parse date manually to avoid timezone issues
		const [year, month, day] = validUntil.split("-").map(Number);
		const validDate = new Date(year, month - 1, day);
		const today = new Date();
		today.setHours(0, 0, 0, 0); // Reset time to start of day for fair comparison
		return validDate < today;
	};

	const getStatusColor = (coupon: DiscountCoupon) => {
		if (!coupon.is_active) return "bg-red-100 text-red-800";
		if (isExpired(coupon.valid_until)) return "bg-orange-100 text-orange-800";
		if (coupon.usage_limit && coupon.current_usage >= coupon.usage_limit) return "bg-gray-100 text-gray-800";
		return "bg-green-100 text-green-800";
	};

	const getStatusText = (coupon: DiscountCoupon) => {
		if (!coupon.is_active) return "Inactif";
		if (isExpired(coupon.valid_until)) return "Expiré";
		if (coupon.usage_limit && coupon.current_usage >= coupon.usage_limit) return "Épuisé";
		return "Actif";
	};

	if (loading) {
		return (
			<div className="p-6">
				<div className="flex items-center justify-center h-64">
					<Loader2 className="h-8 w-8 animate-spin text-emerald-600" />
				</div>
			</div>
		);
	}

	return (
		<div className="p-6">
			<div className="flex justify-between items-center mb-8">
				<div>
					<h1 className="text-3xl font-bold text-gray-900 mb-2">Codes de Réduction</h1>
					<p className="text-gray-600">Gérez vos codes promotionnels et offres spéciales</p>
				</div>
				<Button onClick={handleCreate} icon={Plus}>
					Nouveau Code
				</Button>
			</div>

			{/* Error Message */}
			{error && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
					<div className="flex items-center">
						<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
						<span className="text-red-700">{error}</span>
					</div>
				</div>
			)}

			{/* Search and Filters */}
			<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
				<div className="flex flex-col lg:flex-row gap-4">
					{/* Search */}
					<div className="flex-1">
						<div className="relative">
							<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
							<input
								type="text"
								placeholder="Rechercher par code ou description..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							/>
						</div>
					</div>

					{/* Filters */}
					<div className="flex gap-4">
						<select
							value={statusFilter}
							onChange={(e) => setStatusFilter(e.target.value as any)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="all">Tous les statuts</option>
							<option value="active">Actifs</option>
							<option value="inactive">Inactifs</option>
						</select>

						<select
							value={typeFilter}
							onChange={(e) => setTypeFilter(e.target.value as any)}
							className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
						>
							<option value="all">Tous les types</option>
							<option value="percentage">Pourcentage</option>
							<option value="fixed">Montant fixe</option>
						</select>
					</div>

					{/* Bulk Actions */}
					{selectedCoupons.length > 0 && (
						<div className="flex gap-2">
							<Button
								onClick={handleBulkDelete}
								variant="outline"
								size="sm"
								className="text-red-600 hover:text-red-700"
							>
								<Trash2 className="h-4 w-4 mr-2" />
								Supprimer ({selectedCoupons.length})
							</Button>
						</div>
					)}
				</div>

				{/* Results count */}
				<div className="mt-4 text-sm text-gray-600">
					{filteredCoupons.length} code(s) trouvé(s) sur {coupons.length} total
				</div>
			</div>

			{/* Create/Edit Form */}
			{(isCreating || editingId) && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
					<h2 className="text-xl font-bold text-gray-900 mb-6">
						{isCreating ? "Créer un code de réduction" : "Modifier le code de réduction"}
					</h2>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Code *</label>
							<input
								type="text"
								value={editForm.code || ""}
								onChange={(e) => handleInputChange("code", e.target.value.toUpperCase())}
								className={getFieldClassName(
									"code",
									"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								)}
								placeholder="Ex: SUMMER2024, WELCOME10..."
							/>
							{fieldErrors.code && <p className="mt-1 text-sm text-red-600">{fieldErrors.code}</p>}
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Type de réduction *</label>
							<select
								value={editForm.discount_type || "percentage"}
								onChange={(e) => handleInputChange("discount_type", e.target.value)}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							>
								<option value="percentage">Pourcentage</option>
								<option value="fixed">Montant fixe</option>
							</select>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Valeur de réduction *
							</label>
							<div className="relative">
								<input
									type="number"
									step="0.01"
									min="0"
									max={editForm.discount_type === "percentage" ? "100" : undefined}
									value={editForm.discount_value || ""}
									onChange={(e) => handleInputChange("discount_value", parseFloat(e.target.value))}
									className={getFieldClassName(
										"discount_value",
										"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
									)}
								/>
								<div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
									{editForm.discount_type === "percentage" ? (
										<Percent className="h-4 w-4 text-gray-400" />
									) : (
										<span className="text-gray-400">€</span>
									)}
								</div>
							</div>
							{fieldErrors.discount_value && (
								<p className="mt-1 text-sm text-red-600">{fieldErrors.discount_value}</p>
							)}
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Limite d'utilisation</label>
							<input
								type="number"
								min="1"
								value={editForm.usage_limit || ""}
								onChange={(e) =>
									handleInputChange(
										"usage_limit",
										e.target.value ? parseInt(e.target.value) : undefined
									)
								}
								className={getFieldClassName(
									"usage_limit",
									"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
								)}
								placeholder="Laisser vide pour illimité"
							/>
							{fieldErrors.usage_limit && (
								<p className="mt-1 text-sm text-red-600">{fieldErrors.usage_limit}</p>
							)}
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Montant minimum d'achat (€)
							</label>
							<input
								type="number"
								step="0.01"
								min="0"
								value={editForm.min_purchase_amount || ""}
								onChange={(e) =>
									handleInputChange(
										"min_purchase_amount",
										e.target.value ? parseFloat(e.target.value) : undefined
									)
								}
								className={getFieldClassName(
									"min_purchase_amount",
									"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
								)}
								placeholder="Aucun minimum"
							/>
							{fieldErrors.min_purchase_amount && (
								<p className="mt-1 text-sm text-red-600">{fieldErrors.min_purchase_amount}</p>
							)}
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Réduction maximum (€)
							</label>
							<input
								type="number"
								step="0.01"
								min="0"
								value={editForm.max_discount_amount || ""}
								onChange={(e) =>
									handleInputChange(
										"max_discount_amount",
										e.target.value ? parseFloat(e.target.value) : undefined
									)
								}
								className={getFieldClassName(
									"max_discount_amount",
									"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
								)}
								placeholder="Aucune limite"
							/>
							{fieldErrors.max_discount_amount && (
								<p className="mt-1 text-sm text-red-600">{fieldErrors.max_discount_amount}</p>
							)}
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Date de début <span className="text-gray-500 font-normal">(optionnel)</span>
							</label>
							<input
								type="date"
								value={editForm.valid_from || ""}
								onChange={(e) => handleInputChange("valid_from", e.target.value)}
								className={getFieldClassName(
									"valid_from",
									"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								)}
								placeholder="Aucune date de début"
							/>
							{fieldErrors.valid_from && (
								<p className="mt-1 text-sm text-red-600">{fieldErrors.valid_from}</p>
							)}
							<p className="text-xs text-gray-500 mt-1">
								Laissez vide pour aucune restriction de date de début
							</p>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Date de fin <span className="text-gray-500 font-normal">(optionnel)</span>
							</label>
							<input
								type="date"
								value={editForm.valid_until || ""}
								onChange={(e) => handleInputChange("valid_until", e.target.value)}
								className={getFieldClassName(
									"valid_until",
									"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								)}
								placeholder="Aucune date de fin"
							/>
							{fieldErrors.valid_until && (
								<p className="mt-1 text-sm text-red-600">{fieldErrors.valid_until}</p>
							)}
							<p className="text-xs text-gray-500 mt-1">
								Laissez vide pour aucune restriction de date de fin
							</p>
						</div>

						<div className="md:col-span-2">
							<label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
							<textarea
								value={editForm.description || ""}
								onChange={(e) => handleInputChange("description", e.target.value)}
								rows={3}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
								placeholder="Description du code de réduction..."
							/>
						</div>

						<div className="md:col-span-2">
							<label className="block text-sm font-medium text-gray-700 mb-2">
								Services applicables <span className="text-gray-500 font-normal">(optionnel)</span>
							</label>
							<div className="space-y-2 max-h-40 overflow-y-auto border border-gray-300 rounded-lg p-3">
								<div className="flex items-center">
									<input
										type="checkbox"
										id="all_services"
										checked={
											!editForm.applicable_services || editForm.applicable_services.length === 0
										}
										onChange={(e) => {
											if (e.target.checked) {
												handleInputChange("applicable_services", []);
											}
										}}
										className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
									/>
									<label
										htmlFor="all_services"
										className="ml-2 block text-sm font-medium text-gray-900"
									>
										Tous les services
									</label>
								</div>
								{services.map((service) => (
									<div key={service.id} className="flex items-center">
										<input
											type="checkbox"
											id={`service_${service.id}`}
											checked={editForm.applicable_services?.includes(service.id) || false}
											onChange={(e) => {
												const currentServices = editForm.applicable_services || [];
												if (e.target.checked) {
													handleInputChange("applicable_services", [
														...currentServices,
														service.id,
													]);
												} else {
													handleInputChange(
														"applicable_services",
														currentServices.filter((id) => id !== service.id)
													);
												}
											}}
											className="h-4 w-4 text-emerald-600 focus:ring-emerald-500 border-gray-300 rounded"
										/>
										<label
											htmlFor={`service_${service.id}`}
											className="ml-2 block text-sm text-gray-700"
										>
											{service.name} <span className="text-gray-500">({service.category})</span>
										</label>
									</div>
								))}
							</div>
							<p className="text-xs text-gray-500 mt-1">
								Laissez vide ou sélectionnez "Tous les services" pour appliquer à tous les services
							</p>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">Statut</label>
							<select
								value={editForm.is_active ? "true" : "false"}
								onChange={(e) => handleInputChange("is_active", e.target.value === "true")}
								className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
							>
								<option value="true">Actif</option>
								<option value="false">Inactif</option>
							</select>
						</div>
					</div>

					{/* Validation Summary */}
					{Object.keys(fieldErrors).length > 0 && (
						<div className="bg-red-50 border border-red-200 rounded-lg p-4 mt-6">
							<h4 className="text-sm font-medium text-red-800 mb-2">
								Veuillez corriger les erreurs suivantes :
							</h4>
							<ul className="text-sm text-red-700 space-y-1">
								{Object.entries(fieldErrors).map(([field, error]) => (
									<li key={field}>• {error}</li>
								))}
							</ul>
						</div>
					)}

					<div className="flex justify-end gap-4 mt-6 pt-6 border-t">
						<Button variant="outline" onClick={handleCancel} icon={X} disabled={saving}>
							Annuler
						</Button>
						<Button
							onClick={handleSave}
							icon={saving ? Loader2 : Save}
							disabled={saving || !isFormValid}
							className={!isFormValid ? "opacity-50 cursor-not-allowed" : ""}
						>
							{saving ? "Sauvegarde..." : isCreating ? "Créer le code" : "Sauvegarder"}
						</Button>
					</div>
				</div>
			)}

			{/* Coupons List */}
			{!loading && (
				<div className="bg-white rounded-xl shadow-sm border border-gray-200">
					<div className="p-6 border-b border-gray-200">
						<h2 className="text-xl font-bold text-gray-900">Codes de réduction ({coupons.length})</h2>
					</div>
					<div className="divide-y divide-gray-200">
						{coupons.length === 0 ? (
							<div className="p-12 text-center">
								<Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
								<p className="text-gray-500">Aucun code de réduction trouvé</p>
							</div>
						) : (
							coupons.map((coupon) => (
								<div key={coupon.id} className="p-6">
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<div className="flex items-center gap-3 mb-2">
												<Tag className="h-5 w-5 text-emerald-600" />
												<h3 className="text-lg font-semibold text-gray-900 font-mono">
													{coupon.code}
												</h3>
												<span
													className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(
														coupon
													)}`}
												>
													{getStatusText(coupon)}
												</span>
											</div>
											{coupon.description && (
												<p className="text-gray-600 text-sm mb-3">{coupon.description}</p>
											)}
											{coupon.applicable_services && coupon.applicable_services.length > 0 && (
												<div className="mb-3">
													<span className="text-gray-500 text-sm">Services applicables:</span>
													<div className="flex flex-wrap gap-1 mt-1">
														{coupon.applicable_services.map((serviceId) => {
															const service = services.find((s) => s.id === serviceId);
															return service ? (
																<span
																	key={serviceId}
																	className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
																>
																	{service.name}
																</span>
															) : null;
														})}
													</div>
												</div>
											)}
											<div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
												<div>
													<span className="text-gray-500">Réduction:</span>
													<p className="font-medium text-emerald-600">
														{getDiscountText(coupon.discount_type, coupon.discount_value)}
													</p>
												</div>
												<div>
													<span className="text-gray-500">Utilisations:</span>
													<p className="font-medium">
														{coupon.current_usage}
														{coupon.usage_limit ? ` / ${coupon.usage_limit}` : " / ∞"}
													</p>
												</div>
												<div>
													<span className="text-gray-500">Valide du:</span>
													<p className="font-medium">{formatDate(coupon.valid_from)}</p>
												</div>
												<div>
													<span className="text-gray-500">Valide jusqu'au:</span>
													<p className="font-medium">{formatDate(coupon.valid_until)}</p>
												</div>
											</div>
										</div>
										<div className="flex gap-2 ml-4">
											<Button
												variant="outline"
												size="sm"
												icon={Edit}
												onClick={() => handleEdit(coupon)}
												disabled={saving}
											>
												Modifier
											</Button>
											<Button
												variant="outline"
												size="sm"
												icon={Trash2}
												onClick={() => handleDelete(coupon.id)}
												className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
												disabled={saving}
											>
												Supprimer
											</Button>
										</div>
									</div>
								</div>
							))
						)}
					</div>
				</div>
			)}
		</div>
	);
};

export default AdminDiscountCoupons;
