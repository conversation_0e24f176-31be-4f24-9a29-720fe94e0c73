-- Migration: Add admin tracking fields to reservations table
-- This adds fields to track when reservations are created by admin users

-- Add admin tracking columns to reservations table
ALTER TABLE reservations 
ADD COLUMN IF NOT EXISTS created_by_admin BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS created_by_admin_id UUID REFERENCES profiles(id);

-- Add index for better query performance on admin-created reservations
CREATE INDEX IF NOT EXISTS idx_reservations_created_by_admin 
ON reservations(created_by_admin) 
WHERE created_by_admin = TRUE;

-- Add index for admin user lookups
CREATE INDEX IF NOT EXISTS idx_reservations_created_by_admin_id 
ON reservations(created_by_admin_id) 
WHERE created_by_admin_id IS NOT NULL;

-- Add comment to document the purpose of these fields
COMMENT ON COLUMN reservations.created_by_admin IS 'Indicates if this reservation was created by an admin user rather than through the public booking flow';
COMMENT ON COLUMN reservations.created_by_admin_id IS 'References the admin user who created this reservation (if created_by_admin is true)';

-- Update existing reservations that were created by admin (based on admin_notes pattern)
UPDATE reservations 
SET created_by_admin = TRUE 
WHERE admin_notes LIKE '[CRÉÉ PAR ADMIN:%'
AND created_by_admin IS NOT TRUE;

-- Note: created_by_admin_id cannot be automatically populated for existing records
-- as we don't have a reliable way to extract the admin user ID from the admin_notes field
