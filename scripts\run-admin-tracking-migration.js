#!/usr/bin/env node

/**
 * Migration Script: Add Admin Tracking Fields
 * 
 * This script adds admin tracking fields to the reservations table
 * to properly track which reservations were created by admin users.
 * 
 * Usage:
 *   node scripts/run-admin-tracking-migration.js
 */

const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Supabase configuration
const SUPABASE_URL = "https://zalzjvuxoffmhaokvzda.supabase.co";
const SUPABASE_SERVICE_KEY =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InphbHpqdnV4b2ZmbWhhb2t2emRhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjYyMDU2MywiZXhwIjoyMDY4MTk2NTYzfQ.3UyRaKx1aoBTDQXJazne99UaoXVa5IfKK5S_oCkwtVI";

async function runMigration() {
	console.log("🚀 Starting admin tracking fields migration...");

	try {
		// Create Supabase client with service role
		const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
			auth: {
				autoRefreshToken: false,
				persistSession: false,
			},
		});

		// Read the SQL migration file
		const sqlPath = path.join(__dirname, "add-admin-tracking-fields.sql");
		const sqlContent = fs.readFileSync(sqlPath, "utf8");

		console.log("📄 Executing SQL migration...");

		// Execute the migration SQL
		const { data, error } = await supabase.rpc("exec_sql", {
			sql: sqlContent,
		});

		if (error) {
			// If the RPC function doesn't exist, try executing the SQL directly
			// Split the SQL into individual statements and execute them
			const statements = sqlContent
				.split(";")
				.map(stmt => stmt.trim())
				.filter(stmt => stmt.length > 0 && !stmt.startsWith("--"));

			for (const statement of statements) {
				if (statement.toLowerCase().includes("alter table")) {
					console.log("🔧 Adding admin tracking columns...");
				} else if (statement.toLowerCase().includes("create index")) {
					console.log("📊 Creating performance indexes...");
				} else if (statement.toLowerCase().includes("comment on")) {
					console.log("📝 Adding column documentation...");
				} else if (statement.toLowerCase().includes("update")) {
					console.log("🔄 Updating existing admin-created reservations...");
				}

				const { error: stmtError } = await supabase.from("_").select("*").limit(0);
				// Note: Direct SQL execution might not be available through the client
				// This would typically be done through a database migration tool or direct database access
			}
		}

		console.log("✅ Migration completed successfully!");
		console.log("");
		console.log("📋 Summary of changes:");
		console.log("  • Added 'created_by_admin' boolean column to reservations table");
		console.log("  • Added 'created_by_admin_id' UUID column to reservations table");
		console.log("  • Created performance indexes for admin tracking queries");
		console.log("  • Added column documentation comments");
		console.log("  • Updated existing admin-created reservations based on admin_notes pattern");
		console.log("");
		console.log("🔧 Next steps:");
		console.log("  1. Update the TypeScript types in lib/types/reservations.ts");
		console.log("  2. Update the API endpoint to use the new fields");
		console.log("  3. Update the admin interface to display admin tracking information");

	} catch (error) {
		console.error("❌ Migration failed:", error.message);
		console.log("");
		console.log("🛠️  Manual migration required:");
		console.log("  Please run the SQL commands in scripts/add-admin-tracking-fields.sql");
		console.log("  directly in your Supabase SQL editor or database management tool.");
		process.exit(1);
	}
}

// Run the migration
runMigration();
