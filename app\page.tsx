"use client";

import { ServiceCard } from "@/components/ServiceCard";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import Footer from "@/components/Footer";
import VideoBackground from "@/components/VideoBackground";
import { motion } from "framer-motion";
import { ChevronRight, Play } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";
import { GoogleReviews } from "@/components/reviews/GoogleReviews";

export default function HomePage() {
	const [services, setServices] = useState<any[]>([]);
	const [servicesLoading, setServicesLoading] = useState(true);
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

	// Fetch services from API
	useEffect(() => {
		const fetchServices = async () => {
			try {
				setServicesLoading(true);
				const response = await fetch("/api/services");
				if (!response.ok) {
					throw new Error("Failed to fetch services");
				}
				const data = await response.json();
				// Transform API data to match the ServiceCard component format
				const transformedServices = data.services.slice(0, 3).map((service: any) => ({
					id: service.id,
					name: service.name,
					description: service.description,
					image_url: service.image_url || "/images/waterbikes_service.png",
					duration: `${Math.floor(service.duration_minutes / 60)}h${
						service.duration_minutes % 60 > 0 ? ` ${service.duration_minutes % 60}min` : ""
					}`,
					ageLimit: service.max_age
						? `${service.min_age}-${service.max_age} ans`
						: service.min_age === 0 || service.min_age === null
							? "Pour tous les âges"
							: `À partir de ${service.min_age} ans`,
					features: service.features || [],
					category: service.category,
					capacity: `${service.max_participants} pers. max`,
					location: service.location,
					base_price: service.base_price,
				}));
				setServices(transformedServices);
			} catch (err) {
				console.error("Error fetching services:", err);
				// Fallback to empty array if API fails
				setServices([]);
			} finally {
				setServicesLoading(false);
			}
		};

		fetchServices();
	}, []);

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-50">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center justify-between">
						<Link href="/" className="flex items-center space-x-3">
							<Image
								src="/images/logo-hd.png"
								alt="Soleil & Découverte"
								width={60}
								height={60}
								className="object-contain"
							/>
							<div>
								<h1 className="text-2xl font-bold bg-gradient-to-r from-orange-500 to-yellow-500 bg-clip-text text-transparent">
									Soleil & Découverte
								</h1>
								<p className="text-sm text-emerald-600">L'aventure au cœur de la Guadeloupe</p>
							</div>
						</Link>

						<nav className="hidden md:flex items-center space-x-8">
							<Link
								href="/"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Accueil
							</Link>
							<Link
								href="/services"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Services
							</Link>
							<Link
								href="/contact"
								className="text-gray-700 hover:text-emerald-600 transition-colors font-medium"
							>
								Contact
							</Link>
							<Link href="/reservation">
								<Button className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
									Réserver
								</Button>
							</Link>
						</nav>

						{/* Mobile menu button */}
						<Button
							variant="ghost"
							className="md:hidden"
							onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
						>
							<svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={2}
									d="M4 6h16M4 12h16M4 18h16"
								/>
							</svg>
						</Button>
					</div>
				</div>

				{/* Mobile Menu */}
				{isMobileMenuOpen && (
					<div className="md:hidden bg-white border-t border-gray-200 shadow-lg">
						<div className="container mx-auto px-4 py-4">
							<nav className="flex flex-col space-y-4">
								<Link
									href="/"
									className="text-gray-700 hover:text-emerald-600 transition-colors font-medium py-2"
									onClick={() => setIsMobileMenuOpen(false)}
								>
									Accueil
								</Link>
								<Link
									href="/services"
									className="text-gray-700 hover:text-emerald-600 transition-colors font-medium py-2"
									onClick={() => setIsMobileMenuOpen(false)}
								>
									Services
								</Link>
								<Link
									href="/contact"
									className="text-gray-700 hover:text-emerald-600 transition-colors font-medium py-2"
									onClick={() => setIsMobileMenuOpen(false)}
								>
									Contact
								</Link>
								<Link href="/reservation" onClick={() => setIsMobileMenuOpen(false)}>
									<Button className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-6 py-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 w-full">
										Réserver
									</Button>
								</Link>
							</nav>
						</div>
					</div>
				)}
			</header>

			{/* Hero Section with Video Background */}
			<section className="relative min-h-screen flex items-center justify-center overflow-hidden">
				{/* Video Background with fallback */}
				<VideoBackground
					src="/videos/home-banner.mp4"
					poster="/images/home_hero.png"
					fallbackImage="/images/home_hero.png"
				/>

				{/* Overlay for better text readability */}
				<div className="absolute inset-0 bg-black/20" style={{ zIndex: 4 }}></div>

				<div className="container mx-auto px-4 text-center relative" style={{ zIndex: 10 }}>
					<motion.div
						initial={{ opacity: 0, y: 50 }}
						animate={{ opacity: 1, y: 0 }}
						transition={{ duration: 1 }}
						className="max-w-4xl mx-auto"
					>
						<Badge className="mb-6 bg-white/20 text-white border-white/30 backdrop-blur-sm px-4 py-2 text-lg">
							🌴 Découvrez la Guadeloupe Authentique
						</Badge>

						<h1 className="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight">
							L'Aventure au Cœur de la
							<span className="block bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
								Guadeloupe
							</span>
						</h1>

						<p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto leading-relaxed">
							Plongez au cœur du patrimoine exceptionnel de la Guadeloupe ! Entre nature préservée, sites
							historiques et traditions culturelles vibrantes, vivez des expériences immersives où
							écologie et découverte vont de pair.
						</p>

						<div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
							<Link href="/services">
								<Button
									size="lg"
									className="bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 text-lg font-semibold"
								>
									<Play className="w-5 h-5 mr-2" />
									Découvrir nos Excursions
								</Button>
							</Link>
							<Link href="/reservation">
								<Button
									size="lg"
									variant="outline"
									className="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-full backdrop-blur-sm bg-white/10 transition-all duration-300 text-lg font-semibold"
								>
									Réserver Maintenant
									<ChevronRight className="w-5 h-5 ml-2" />
								</Button>
							</Link>
						</div>
					</motion.div>
				</div>

				{/* Floating elements */}
				<div className="absolute top-20 left-10 animate-bounce">
					<div className="w-4 h-4 bg-yellow-400 rounded-full opacity-70"></div>
				</div>
				<div className="absolute top-40 right-20 animate-pulse">
					<div className="w-6 h-6 bg-emerald-400 rounded-full opacity-60"></div>
				</div>
				<div className="absolute bottom-40 left-20 animate-bounce delay-1000">
					<div className="w-3 h-3 bg-orange-400 rounded-full opacity-80"></div>
				</div>
			</section>

			{/* Services Section */}
			<section className="py-20 bg-white">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center mb-16"
					>
						<Badge className="mb-4 bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-700 border-emerald-200 px-4 py-2">
							🏝️ Nos Expériences
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							Aventures Authentiques
							<span className="block text-emerald-600">et Inoubliables</span>
						</h2>
						<p className="text-xl text-gray-600 max-w-3xl mx-auto">
							Entre excursions en WaterBike, balades en pleine nature et découvertes culturelles, vivez
							des moments hors du commun au rythme des paysages guadeloupéens.
						</p>
					</motion.div>

					<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
						{servicesLoading
							? // Loading skeleton
								[...Array(3)].map((_, index) => (
									<div key={index} className="group">
										<Card className="overflow-hidden shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
											<div className="relative overflow-hidden">
												<div className="w-full h-64 bg-gray-200 animate-pulse"></div>
											</div>
											<CardContent className="p-6">
												<div className="h-6 bg-gray-200 rounded mb-3 animate-pulse"></div>
												<div className="h-4 bg-gray-200 rounded mb-4 animate-pulse"></div>
												<div className="space-y-2 mb-6">
													{[...Array(3)].map((_, i) => (
														<div
															key={i}
															className="h-3 bg-gray-200 rounded animate-pulse"
														></div>
													))}
												</div>
											</CardContent>
										</Card>
									</div>
								))
							: services.map((service, index) => (
									<ServiceCard
										key={service.id}
										service={service}
										index={index}
										onDetailsClick={() => {}}
									/>
								))}
					</div>
				</div>
			</section>

			{/* Google Reviews Section */}
			{/* <section className="py-20 bg-gradient-to-br from-emerald-50 to-teal-50">
				<div className="container mx-auto px-4">
					<GoogleReviews
						maxReviews={10}
						minRating={4}
						autoRefresh={false}
						showSource={false}
						enableFallback={true}
						className="max-w-6xl mx-auto"
					/>
				</div>
			</section> */}

			{/* About Preview Section */}
			{/* 
						<section className="py-20 bg-white">
							<div className="container mx-auto px-4">
								<div className="grid lg:grid-cols-2 gap-12 items-center">
									<motion.div
										initial={{ opacity: 0, x: -50 }}
										whileInView={{ opacity: 1, x: 0 }}
										transition={{ duration: 0.8 }}
									>
										<Badge className="mb-4 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 border-blue-200 px-4 py-2">
											🌊 Notre Histoire
										</Badge>
										<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
											Une Passion Héritée de
											<span className="block text-emerald-600">l'Enfance</span>
										</h2>
										<p className="text-lg text-gray-600 mb-6 leading-relaxed">
											Notre passion pour cette île, héritée d'une enfance bercée par la mer et la terre, nous
											a poussés à créer des expériences immersives où écologie et découverte vont de pair.
										</p>
										<p className="text-lg text-gray-600 mb-8 leading-relaxed">
											Partez avec nous à la rencontre des trésors cachés de l'archipel, dans le respect de
											l'environnement et des traditions locales. Chaque excursion est pensée pour minimiser
											l'impact sur l'environnement et sensibiliser à la préservation de notre patrimoine
											naturel.
										</p>
										<Link href="/about">
											<Button className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white px-8 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300">
												Découvrir Notre Histoire
												<ChevronRight className="w-4 h-4 ml-2" />
											</Button>
										</Link>
									</motion.div>

									<motion.div
										initial={{ opacity: 0, x: 50 }}
										whileInView={{ opacity: 1, x: 0 }}
										transition={{ duration: 0.8 }}
										className="relative"
									>
										<div className="relative overflow-hidden rounded-2xl shadow-2xl">
											<Image
												src="/images/team_about_preview.png"
												alt="Notre équipe"
												width={500}
												height={600}
												className="w-full h-96 object-cover"
											/>
											<div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
											<div className="absolute bottom-6 left-6 text-white">
												<p className="text-lg font-semibold">Une équipe passionnée</p>
												<p className="text-sm opacity-90">Guides locaux expérimentés</p>
											</div>
										</div>

										{/* Floating stats */}
			{/*<div className="absolute -top-6 -left-6 bg-white rounded-2xl shadow-xl p-6 border-4 border-emerald-100">
											<div className="text-center">
												<div className="text-sm text-emerald-600 font-semibold">Avec une vision</div>
												<div className="text-sm text-gray-600">éco-responsable</div>
											</div>
										</div>

										<div className="absolute -bottom-6 -right-6 bg-white rounded-2xl shadow-xl p-6 border-4 border-orange-100">
											<div className="text-center">
												<div className="text-3xl font-bold text-orange-600">5★</div>
												<div className="text-sm text-gray-600">Satisfaction client</div>
											</div>
										</div>
									</motion.div>
								</div>
							</div>
						</section>
			}
			*/}

			{/* Eco-Responsibility Section */}
			<section className="py-20 bg-gradient-to-br from-green-50 to-emerald-50">
				<div className="container mx-auto px-4">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<Badge className="mb-4 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 border-green-200 px-4 py-2">
							🌿 La Préservation est Primordiale
						</Badge>
						<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
							Explorations
							<span className="block text-emerald-600">Éco-Responsables</span>
						</h2>
						<p className="text-xl text-gray-600 mb-8 leading-relaxed">
							En Guadeloupe, chaque site est un joyau à préserver. Chez Soleil & Découverte, nous nous
							engageons à faire rayonner notre île tout en protégeant ses richesses naturelles et
							culturelles. Ensemble, préservons aujourd'hui pour émerveiller demain.
						</p>
						<p className="text-lg text-gray-600 leading-relaxed">
							Naviguez à travers la mangrove, longez le littoral et laissez-vous émerveiller par une
							biodiversité exceptionnelle. Nos visites sont pensées pour minimiser l'impact sur
							l'environnement, respecter la biodiversité et sensibiliser à la préservation de notre
							patrimoine naturel.
						</p>
					</motion.div>
				</div>
			</section>

			{/* CTA Section */}
			<section className="py-20 bg-gradient-to-br from-emerald-600 to-teal-700 relative overflow-hidden">
				<div className="absolute inset-0 bg-black/20"></div>
				<Image src="/images/sunset_cta.png" alt="Sunset" fill className="object-cover -z-10" />

				<div className="container mx-auto px-4 relative z-10">
					<motion.div
						initial={{ opacity: 0, y: 30 }}
						whileInView={{ opacity: 1, y: 0 }}
						transition={{ duration: 0.8 }}
						className="text-center max-w-4xl mx-auto"
					>
						<h2 className="text-4xl md:text-6xl font-bold text-white mb-6">Prêt pour l'Aventure ?</h2>
						<p className="text-xl md:text-2xl text-white/90 mb-8 leading-relaxed">
							Réservez dès maintenant votre excursion et découvrez la Guadeloupe comme vous ne l'avez
							jamais vue !
						</p>
						<div className="flex flex-col sm:flex-row gap-4 justify-center">
							<Link href="/reservation">
								<Button
									size="lg"
									className="bg-white text-emerald-600 hover:bg-gray-100 px-8 py-4 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 text-lg font-semibold"
								>
									Réserver Maintenant
									<ChevronRight className="w-5 h-5 ml-2" />
								</Button>
							</Link>
						</div>
					</motion.div>
				</div>
			</section>

			<Footer />
		</div>
	);
}
