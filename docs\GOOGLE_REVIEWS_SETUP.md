# Google Reviews Integration Setup Guide

This guide explains how to set up and configure the Google Reviews integration for the Soleil et Découverte website using the Google Business Profile API.

## Overview

The Google Reviews integration provides:

- **Real Google Reviews**: Fetches authentic customer reviews from Google Business Profile
- **Unlimited Reviews**: Access to all customer reviews with pagination
- **Fallback System**: Shows cached reviews or default testimonials when API is unavailable
- **Admin Management**: Interface to manage review display and refresh data
- **Compliance**: Follows Google's API terms of service and display policies

## Google Business Profile API

- ✅ Unlimited reviews with full data
- ✅ Complete review management capabilities
- ✅ Reply to reviews functionality
- ✅ Real-time updates
- ❌ Requires 2-4 week approval process
- ❌ Business verification needed

## Configuration Steps

### 1. Google Cloud Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the "Google My Business API" (now called Google Business Profile API)
4. Apply for API access at [Google Business Profile API](https://developers.google.com/my-business/content/prereqs#request-access)
5. Set up OAuth 2.0 credentials for web application
6. Add your domain to authorized redirect URIs

### 2. Business Verification

1. Ensure your Google Business Profile is verified
2. You must be the owner or manager of the business profile
3. The business should have existing reviews to fetch

### 3. Find Your Business Account and Location IDs

1. Use the Google Business Profile API to list your accounts
2. Find your account ID and location ID
3. These will be needed for API calls

### 4. Environment Variables

Add these to your `.env.local` file:

```env
# Google Business Profile API
GOOGLE_BUSINESS_PROFILE_CLIENT_ID=your_client_id_here
GOOGLE_BUSINESS_PROFILE_CLIENT_SECRET=your_client_secret_here
GOOGLE_BUSINESS_ACCOUNT_ID=your_account_id_here
GOOGLE_BUSINESS_LOCATION_ID=your_location_id_here
```

### 4. Database Setup

The database schema is already created with these tables:

- `cached_reviews`: Stores Google reviews locally
- Includes proper indexing and RLS policies

## Usage

### Frontend Integration

The homepage now uses the `GoogleReviews` component:

```tsx
<GoogleReviews
	maxReviews={10}
	minRating={4}
	autoRefresh={false}
	showSource={false}
	enableFallback={true}
	className="max-w-6xl mx-auto"
/>
```

### API Endpoints

- `GET /api/reviews` - Fetch reviews for public display
- `GET /api/admin/reviews` - Admin: List all cached reviews
- `POST /api/admin/reviews` - Admin: Refresh from Google API
- `PATCH /api/admin/reviews` - Admin: Bulk update review visibility
- `DELETE /api/admin/reviews` - Admin: Delete reviews

### Admin Interface

Access the reviews management at `/admin` (requires admin authentication):

- View all cached reviews
- Manually refresh from Google API
- Toggle review visibility
- Filter and search reviews
- Bulk operations

## Testing

### 1. Test Fallback System

Without API keys configured, the system should show default testimonials.

### 2. Test with Sample Data

The database includes sample reviews for testing the display.

### 3. Test API Integration

Once API keys are configured:

1. Visit the homepage to see reviews
2. Check admin interface for management
3. Test manual refresh functionality

## Compliance with Google Policies

### Display Requirements

- ✅ Shows authentic review data
- ✅ Includes author names (or initials for privacy)
- ✅ Displays star ratings accurately
- ✅ Shows review dates
- ✅ Maintains review text integrity

### API Usage

- ✅ Respects rate limits
- ✅ Caches data to reduce API calls
- ✅ Handles errors gracefully
- ✅ Provides fallback content

### Privacy

- ✅ Can show initials instead of full names
- ✅ Respects user profile photo settings
- ✅ Allows review moderation

## Troubleshooting

### Common Issues

1. **No reviews showing**
    - Check API key configuration
    - Verify Place ID is correct
    - Check browser console for errors

2. **API quota exceeded**
    - Reviews are cached to minimize API calls
    - Consider upgrading Google Cloud plan

3. **Reviews not updating**
    - Use admin interface to manually refresh
    - Check API key permissions

### Error Handling

The system includes comprehensive error handling:

- Network failures → Fallback to cached reviews
- API errors → Show fallback testimonials
- Invalid configuration → Display error messages

## Implementation Status

### Current State

- Google Business Profile API integration (ready for configuration)
- Fallback testimonials system (active)
- Admin management interface (ready)
- OAuth 2.0 authentication (needs implementation)

### Next Steps

1. Apply for Google Business Profile API access
2. Implement OAuth 2.0 authentication flow
3. Configure environment variables
4. Test with real business data

## OAuth 2.0 Implementation

The current implementation includes a placeholder for OAuth 2.0 authentication. To complete the integration, you need to:

### 1. Implement OAuth Flow

```typescript
// In lib/google-auth.ts
export async function getGoogleAccessToken(): Promise<string> {
	// Implement OAuth 2.0 flow
	// 1. Redirect user to Google authorization URL
	// 2. Handle callback with authorization code
	// 3. Exchange code for access token
	// 4. Store and refresh tokens as needed
}
```

### 2. Update the Service

Replace the placeholder `getAccessToken()` method in `GoogleReviewsService` with your OAuth implementation.

### 3. Add Authentication Routes

Create API routes for:

- `/api/auth/google` - Initiate OAuth flow
- `/api/auth/google/callback` - Handle OAuth callback
- `/api/auth/refresh` - Refresh access tokens

## Support

For technical support or questions about the Google Reviews integration:

1. Check the troubleshooting section above
2. Review Google's API documentation
3. Contact the development team

## Security Notes

- API keys should never be exposed to client-side code
- Use environment variables for all credentials
- Regularly rotate API keys
- Monitor API usage in Google Cloud Console
