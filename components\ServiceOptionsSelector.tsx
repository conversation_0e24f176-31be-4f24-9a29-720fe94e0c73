"use client";

import { useEffect, useState, useCallback } from "react";
import {
	ServiceOptionAssignment,
	OptionSelection,
	ServiceOption,
	RequiredOptionGroup,
} from "@/lib/types/service-options";
import { validateOptionSelections, calculateOptionsPricing } from "@/lib/service-options";

interface ServiceOptionsSelectorProps {
	assignments: ServiceOptionAssignment[];
	selectedOptions: OptionSelection[];
	onSelectionChange: (selectedOptions: OptionSelection[]) => void;
	onPriceChange: (totalOptionsPrice: number) => void;
	onValidationChange?: (isValid: boolean, errors: string[]) => void;
	participantCount?: number;
	requiredGroups?: RequiredOptionGroup[];
	showValidation?: boolean;
	showValidationErrors?: boolean; // New prop to control when to show validation errors
}

export default function ServiceOptionsSelector({
	assignments,
	selectedOptions,
	onSelectionChange,
	onPriceChange,
	onValidationChange,
	participantCount = 1,
	requiredGroups = [],
	showValidation = false,
	showValidationErrors = false,
}: ServiceOptionsSelectorProps) {
	const [selections, setSelections] = useState<OptionSelection[]>(selectedOptions);
	const [validation, setValidation] = useState(
		validateOptionSelections(assignments, selectedOptions, requiredGroups)
	);
	const [errors, setErrors] = useState<string[]>([]);

	// Update when props change
	useEffect(() => {
		setSelections(selectedOptions);
	}, [selectedOptions]);

	// Validate and calculate pricing whenever selections change
	useEffect(() => {
		const validation = validateOptionSelections(assignments, selections, requiredGroups);
		setValidation(validation);
		setErrors(validation.errors);

		// Notify parent of validation state
		if (onValidationChange) {
			onValidationChange(validation.isValid, validation.errors);
		}

		const pricing = calculateOptionsPricing(assignments, selections, participantCount);
		onPriceChange(pricing.total);
	}, [selections, assignments, participantCount, requiredGroups]);

	const handleOptionToggle = (optionId: string) => {
		const newSelections = [...selections];
		const existingIndex = newSelections.findIndex((s) => s.optionId === optionId);

		if (existingIndex >= 0) {
			// Remove selection
			newSelections.splice(existingIndex, 1);
		} else {
			// Check if this option belongs to a required group with maxRequired = 1 (exactly one selection)
			const belongsToExactlyOneGroup = requiredGroups.find(
				(group) => group.optionIds.includes(optionId) && group.maxRequired === 1
			);

			if (belongsToExactlyOneGroup) {
				// Remove any other selections from this group first
				const otherSelectionsInGroup = newSelections.filter((s) =>
					belongsToExactlyOneGroup.optionIds.includes(s.optionId)
				);
				otherSelectionsInGroup.forEach((selection) => {
					const index = newSelections.findIndex((s) => s.optionId === selection.optionId);
					if (index >= 0) {
						newSelections.splice(index, 1);
					}
				});
			}

			// Add selection
			newSelections.push({
				optionId,
				quantity: 1,
			});
		}

		setSelections(newSelections);
		onSelectionChange(newSelections);

		// Update validation
		const newValidation = validateOptionSelections(assignments, newSelections, requiredGroups);
		setValidation(newValidation);
	};

	const isOptionSelected = (optionId: string): boolean => {
		return selections.some((s) => s.optionId === optionId);
	};

	const getInputTypeForOption = (optionId: string): "checkbox" | "radio" => {
		const belongsToExactlyOneGroup = requiredGroups.find(
			(group) => group.optionIds.includes(optionId) && group.maxRequired === 1
		);
		return belongsToExactlyOneGroup ? "radio" : "checkbox";
	};

	const getRadioGroupName = (optionId: string): string | undefined => {
		const group = requiredGroups.find((group) => group.optionIds.includes(optionId) && group.maxRequired === 1);
		return group ? `radio-group-${group.id}` : undefined;
	};

	const getOptionPrice = (option: ServiceOption): number => {
		const finalPrice = option.customPrice ?? option.basePrice;
		return option.perParticipant ? finalPrice * participantCount : finalPrice;
	};

	const formatPrice = (option: ServiceOption): string => {
		if (option.quoteBased && option.basePrice === 0) {
			return "sur devis";
		}

		const price = getOptionPrice(option);
		const priceText = `${price.toFixed(2)}€`;

		if (option.perParticipant && participantCount > 1) {
			return `${priceText} (${option.basePrice.toFixed(2)}€ × ${participantCount})`;
		}

		return priceText;
	};

	// Selection type labels removed - no longer needed without groups

	if (!assignments || assignments.length === 0) {
		return null;
	}

	return (
		<div className="space-y-6">
			<h3 className="text-lg font-semibold text-gray-900">Options du service</h3>

			{/* Validation Errors - Only show when explicitly requested */}
			{showValidationErrors && !validation.isValid && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4">
					<h4 className="font-medium text-red-800 mb-2">Sélections requises manquantes</h4>
					<ul className="text-sm text-red-700 space-y-1">
						{validation.errors.map((error, index) => (
							<li key={index}>• {error}</li>
						))}
					</ul>
				</div>
			)}

			{/* Required Groups Info - Always show as helpful tip */}
			{requiredGroups.length > 0 && (
				<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
					<h4 className="font-medium text-blue-800 mb-2">Sélections requises</h4>
					<ul className="text-sm text-blue-700 space-y-1">
						{requiredGroups.map((group) => (
							<li key={group.id}>
								• <strong>{group.name}:</strong>{" "}
								{group.maxRequired === 1 && group.minRequired === 1
									? "Vous devez sélectionner une option"
									: `Sélectionnez au moins ${group.minRequired} option(s)`}
								{group.description && <span className="text-blue-600"> - {group.description}</span>}
							</li>
						))}
					</ul>
				</div>
			)}

			<div className="space-y-6">
				{assignments.map((assignment) => (
					<div key={assignment.id} className="space-y-3">
						{assignment.option && (
							<div
								className={`border rounded-lg p-4 cursor-pointer transition-colors ${
									isOptionSelected(assignment.option.id)
										? "border-emerald-500 bg-emerald-50"
										: "border-gray-200 hover:border-gray-300"
								}`}
								onClick={() => handleOptionToggle(assignment.option.id)}
							>
								<div className="flex items-start justify-between">
									<div className="flex items-start space-x-3">
										<input
											type={getInputTypeForOption(assignment.option.id)}
											name={getRadioGroupName(assignment.option.id)}
											checked={isOptionSelected(assignment.option.id)}
											onChange={() => {}} // Handled by div click
											className="mt-1 text-emerald-600 focus:ring-emerald-500"
										/>
										<div className="flex-1">
											<div className="flex items-center gap-2">
												<h4 className="font-medium text-gray-900">{assignment.option.name}</h4>
												{assignment.isRequired && (
													<span className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded">
														Obligatoire
													</span>
												)}
											</div>
											{assignment.option.description && (
												<p className="text-sm text-gray-600 mt-1">
													{assignment.option.description}
												</p>
											)}
										</div>
									</div>
									<div className="text-right">
										<span className="font-medium text-gray-900">
											{formatPrice(assignment.option)}
										</span>
									</div>
								</div>
							</div>
						)}
					</div>
				))}
			</div>
		</div>
	);
}
