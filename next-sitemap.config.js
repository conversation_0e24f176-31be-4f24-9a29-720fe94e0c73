/** @type {import('next-sitemap').IConfig} */
module.exports = {
	siteUrl: process.env.SITE_URL || "https://www.soleiletdecouverte.com",
	generateRobotsTxt: true,
	generateIndexSitemap: false,

	// Exclude admin routes and API routes from sitemap
	exclude: ["/admin/*", "/api/*", "/debug/*", "/verify-booking/*", "/reservation/confirmation"],

	// Additional paths to include (for dynamic routes and static pages)
	additionalPaths: async (config) => {
		const result = [];

		// Add main static pages explicitly (excluding hidden pages with underscore prefix)
		const mainPages = [
			{ loc: "/", priority: 1.0, changefreq: "weekly" },
			{ loc: "/services", priority: 0.9, changefreq: "weekly" },
			{ loc: "/contact", priority: 0.8, changefreq: "monthly" },
			{ loc: "/reservation", priority: 0.9, changefreq: "weekly" },
			// Removed /_about and /_gallery as they are intentionally hidden
		];

		mainPages.forEach((page) => {
			result.push({
				loc: page.loc,
				changefreq: page.changefreq,
				priority: page.priority,
				lastmod: new Date().toISOString(),
			});
		});

		// Service detail pages are now handled dynamically by server-sitemap.xml
		// Remove static service URLs to prevent indexing non-existent pages

		return result;
	},

	// Custom transformation for specific pages
	transform: async (config, path) => {
		// Custom priority and changefreq for different page types
		const customConfig = {
			loc: path,
			lastmod: new Date().toISOString(),
		};

		// Homepage
		if (path === "/") {
			return {
				...customConfig,
				priority: 1.0,
				changefreq: "weekly",
			};
		}

		// Main service pages
		if (path === "/services" || path === "/reservation") {
			return {
				...customConfig,
				priority: 0.9,
				changefreq: "weekly",
			};
		}

		// Contact page
		if (path === "/contact") {
			return {
				...customConfig,
				priority: 0.8,
				changefreq: "monthly",
			};
		}

		// Hidden pages (with underscore prefix) should not be in sitemap
		if (path.startsWith("/_")) {
			return null; // Exclude from sitemap
		}

		// Service detail pages
		if (path.startsWith("/services/")) {
			return {
				...customConfig,
				priority: 0.8,
				changefreq: "monthly",
			};
		}

		// Default configuration
		return {
			...customConfig,
			priority: 0.5,
			changefreq: "monthly",
		};
	},

	// Robots.txt configuration
	robotsTxtOptions: {
		policies: [
			{
				userAgent: "*",
				allow: "/",
				disallow: [
					"/admin/",
					"/api/",
					"/debug/",
					"/verify-booking/",
					"/reservation/confirmation",
					"/_about",
					"/_gallery",
				],
			},
		],
		additionalSitemaps: [
			// Dynamic sitemap for services and other dynamic content
			"https://www.soleiletdecouverte.com/server-sitemap.xml",
		],
	},
};
