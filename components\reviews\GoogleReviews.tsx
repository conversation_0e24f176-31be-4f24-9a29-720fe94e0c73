"use client";

import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { motion } from "framer-motion";
import { MapPin, Star, RefreshCw, AlertCircle } from "lucide-react";
import { useEffect, useState } from "react";
import { FallbackTestimonials } from "./FallbackTestimonials";

export interface Review {
	id: string;
	name: string;
	initials: string;
	rating: number;
	comment: string;
	date: string;
	relativeTime: string;
	profilePhoto?: string;
	source: string;
}

interface GoogleReviewsResponse {
	success: boolean;
	reviews: Review[];
	count: number;
	source: string;
	timestamp: string;
	error?: string;
	details?: string;
}

interface GoogleReviewsProps {
	maxReviews?: number;
	minRating?: number;
	autoRefresh?: boolean;
	refreshInterval?: number; // in minutes
	showSource?: boolean;
	enableFallback?: boolean;
	className?: string;
}

export function GoogleReviews({
	maxReviews = 10,
	minRating = 4,
	autoRefresh = false,
	refreshInterval = 60,
	showSource = false,
	enableFallback = true,
	className = "",
}: GoogleReviewsProps) {
	const [reviews, setReviews] = useState<Review[]>([]);
	const [currentReview, setCurrentReview] = useState(0);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [lastUpdated, setLastUpdated] = useState<string | null>(null);
	const [dataSource, setDataSource] = useState<string>("");

	const fetchReviews = async (refresh = false) => {
		try {
			setLoading(true);
			setError(null);

			const params = new URLSearchParams({
				maxReviews: maxReviews.toString(),
				minRating: minRating.toString(),
			});

			if (refresh) {
				params.set("refresh", "true");
			}

			const response = await fetch(`/api/reviews?${params}`);
			const data: GoogleReviewsResponse = await response.json();

			if (data.success) {
				setReviews(data.reviews);
				setDataSource(data.source);
				setLastUpdated(data.timestamp);
				setError(null);
			} else {
				setError(data.error || "Failed to fetch reviews");
				console.error("Reviews API error:", data.details);
			}
		} catch (err) {
			setError("Network error while fetching reviews");
			console.error("Network error:", err);
		} finally {
			setLoading(false);
		}
	};

	// Initial load
	useEffect(() => {
		fetchReviews();
	}, [maxReviews, minRating]);

	// Auto refresh
	useEffect(() => {
		if (!autoRefresh) return;

		const interval = setInterval(
			() => {
				fetchReviews(true);
			},
			refreshInterval * 60 * 1000
		);

		return () => clearInterval(interval);
	}, [autoRefresh, refreshInterval]);

	// Carousel rotation
	useEffect(() => {
		if (reviews.length <= 1) return;

		const timer = setInterval(() => {
			setCurrentReview((prev) => (prev + 1) % reviews.length);
		}, 5000);

		return () => clearInterval(timer);
	}, [reviews.length]);

	if (loading) {
		return (
			<div className={`flex items-center justify-center p-8 ${className}`}>
				<RefreshCw className="w-6 h-6 animate-spin text-emerald-600" />
				<span className="ml-2 text-gray-600">Chargement des avis...</span>
			</div>
		);
	}

	if (error && enableFallback) {
		return <FallbackTestimonials className={className} showFallbackNotice={true} />;
	}

	if (error) {
		return (
			<div className={`flex items-center justify-center p-8 ${className}`}>
				<AlertCircle className="w-6 h-6 text-red-500" />
				<span className="ml-2 text-red-600">{error}</span>
			</div>
		);
	}

	if (reviews.length === 0 && enableFallback) {
		return <FallbackTestimonials className={className} showFallbackNotice={false} />;
	}

	if (reviews.length === 0) {
		return (
			<div className={`flex items-center justify-center p-8 ${className}`}>
				<span className="text-gray-600">Aucun avis disponible pour le moment.</span>
			</div>
		);
	}

	const currentReviewData = reviews[currentReview];

	return (
		<div className={className}>
			{/* Header */}
			<motion.div
				initial={{ opacity: 0, y: 30 }}
				whileInView={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.8 }}
				className="text-center mb-16"
			>
				<Badge className="mb-4 bg-gradient-to-r from-yellow-100 to-orange-100 text-orange-700 border-orange-200 px-4 py-2">
					⭐ Avis Google
				</Badge>
				<h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
					Ce Que Disent Nos
					<span className="block text-emerald-600">Clients</span>
				</h2>
				<p className="text-xl text-gray-600 max-w-2xl mx-auto">
					Découvrez les témoignages authentiques de nos aventuriers
				</p>
			</motion.div>

			{/* Review Card */}
			<motion.div
				key={currentReview}
				initial={{ opacity: 0, scale: 0.95 }}
				animate={{ opacity: 1, scale: 1 }}
				transition={{ duration: 0.5 }}
				className="text-center"
			>
				<Card className="p-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm max-w-4xl mx-auto">
					<CardContent className="p-0">
						{/* Star Rating */}
						<div className="flex justify-center mb-4">
							{[...Array(currentReviewData.rating)].map((_, i) => (
								<Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
							))}
						</div>

						{/* Review Text */}
						<blockquote className="text-xl md:text-2xl text-gray-700 mb-6 italic leading-relaxed">
							"{currentReviewData.comment}"
						</blockquote>

						{/* Author Info */}
						<div className="flex items-center justify-center space-x-4">
							{/* Profile Photo or Initials */}
							<div className="flex-shrink-0">
								{currentReviewData.profilePhoto ? (
									<img
										src={currentReviewData.profilePhoto}
										alt={currentReviewData.name}
										className="w-12 h-12 rounded-full object-cover"
									/>
								) : (
									<div className="w-12 h-12 rounded-full bg-emerald-100 flex items-center justify-center">
										<span className="text-emerald-700 font-semibold">
											{currentReviewData.initials}
										</span>
									</div>
								)}
							</div>

							<div className="text-center">
								<p className="font-semibold text-gray-900 text-lg">{currentReviewData.name}</p>
								<div className="flex items-center justify-center text-gray-500 text-sm">
									<MapPin className="w-4 h-4 mr-1" />
									{currentReviewData.relativeTime}
								</div>
								{showSource && (
									<Badge variant="outline" className="mt-2 text-emerald-600 border-emerald-200">
										Google Reviews
									</Badge>
								)}
							</div>
						</div>
					</CardContent>
				</Card>
			</motion.div>

			{/* Navigation Dots */}
			{reviews.length > 1 && (
				<div className="flex justify-center mt-8 space-x-2">
					{reviews.map((_, index) => (
						<button
							key={index}
							onClick={() => setCurrentReview(index)}
							className={`w-3 h-3 rounded-full transition-all duration-300 ${
								index === currentReview ? "bg-emerald-600 scale-125" : "bg-gray-300 hover:bg-gray-400"
							}`}
							aria-label={`Voir l'avis ${index + 1}`}
						/>
					))}
				</div>
			)}

			{/* Footer Info */}
			{showSource && lastUpdated && (
				<div className="text-center mt-4 text-xs text-gray-500">
					Dernière mise à jour: {new Date(lastUpdated).toLocaleString("fr-FR")}
					{dataSource && ` • Source: ${dataSource}`}
				</div>
			)}
		</div>
	);
}

export default GoogleReviews;
