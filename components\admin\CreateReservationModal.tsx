"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { <PERSON>, <PERSON><PERSON>2, <PERSON>ert<PERSON>ircle, CheckCircle } from "lucide-react";
import CustomerSearchSelector from "./CustomerSearchSelector";
import ServiceSelector from "./ServiceSelector";
import TimeSlotSelector from "./TimeSlotSelector";
import ParticipantCountSelector from "./ParticipantCountSelector";
import ServiceOptionsSelector from "../ServiceOptionsSelector";
import { adminApi } from "@/lib/api-client";
import { getServiceOptions } from "@/lib/service-options";
import { supabase } from "@/lib/supabase";
import { ServiceOptionAssignment, OptionSelection, RequiredOptionGroup } from "@/lib/types/service-options";

interface Customer {
	id: string;
	first_name?: string;
	last_name?: string;
	email?: string;
	phone?: string;
}

interface Service {
	id: string;
	name: string;
	description?: string;
	duration_minutes: number;
	max_participants: number;
	min_age?: number;
	max_age?: number;
	category?: string;
	location?: string;
	image_url?: string;
	base_price: number;
	fixed_price: boolean;
	pricing_tiers?: Array<{
		id: string;
		tier_name: string;
		price: number;
		min_age?: number;
		max_age?: number;
	}>;
}

interface TimeSlot {
	id: string;
	start_time: string;
	end_time: string;
	available_spots: number;
	is_available: boolean;
}

interface TierParticipant {
	tierId: string;
	count: number;
}

interface CreateReservationModalProps {
	isOpen: boolean;
	onClose: () => void;
	onReservationCreated: (reservationId: string) => void;
}

export default function CreateReservationModal({ isOpen, onClose, onReservationCreated }: CreateReservationModalProps) {
	// Form state
	const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
	const [selectedService, setSelectedService] = useState<Service | null>(null);
	const [selectedDate, setSelectedDate] = useState<Date | null>(null);
	const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(null);
	const [tierParticipants, setTierParticipants] = useState<TierParticipant[]>([]);
	const [selectedOptions, setSelectedOptions] = useState<OptionSelection[]>([]);
	const [specialRequests, setSpecialRequests] = useState("");
	const [adminNotes, setAdminNotes] = useState("");

	// Service options state
	const [serviceOptionAssignments, setServiceOptionAssignments] = useState<ServiceOptionAssignment[]>([]);
	const [requiredGroups, setRequiredGroups] = useState<RequiredOptionGroup[]>([]);
	const [optionsPrice, setOptionsPrice] = useState(0);
	const [loadingOptions, setLoadingOptions] = useState(false);
	const [optionsValid, setOptionsValid] = useState(true);
	const [optionsValidationErrors, setOptionsValidationErrors] = useState<string[]>([]);

	// UI state
	const [currentStep, setCurrentStep] = useState(1);
	const [isCreating, setIsCreating] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [validationErrors, setValidationErrors] = useState<string[]>([]);

	// Reset form when modal opens/closes
	useEffect(() => {
		if (isOpen) {
			resetForm();
		}
	}, [isOpen]);

	// Load service options when service changes
	useEffect(() => {
		if (selectedService) {
			loadServiceOptions();
		} else {
			setServiceOptionAssignments([]);
			setRequiredGroups([]);
			setSelectedOptions([]);
			setOptionsPrice(0);
		}
	}, [selectedService]);

	const resetForm = () => {
		setSelectedCustomer(null);
		setSelectedService(null);
		setSelectedDate(null);
		setSelectedTimeSlot(null);
		setTierParticipants([]);
		setSelectedOptions([]);
		setSpecialRequests("");
		setAdminNotes("");
		setCurrentStep(1);
		setError(null);
		setValidationErrors([]);
		setServiceOptionAssignments([]);
		setRequiredGroups([]);
		setOptionsPrice(0);
	};

	const loadServiceOptions = useCallback(async () => {
		if (!selectedService) return;

		setLoadingOptions(true);
		try {
			const assignments = await getServiceOptions(supabase, selectedService.id);
			setServiceOptionAssignments(assignments);

			// Load required groups from the API
			try {
				const groupsData = await adminApi.getServiceRequiredGroups(selectedService.id);
				setRequiredGroups(groupsData.requiredGroups || []);
			} catch (error) {
				console.error("Error loading required groups:", error);
				setRequiredGroups([]);
			}
		} catch (error) {
			console.error("Error loading service options:", error);
		} finally {
			setLoadingOptions(false);
		}
	}, [selectedService]);

	const handleCreateNewCustomer = async (customerData: Partial<Customer>) => {
		try {
			const response = await adminApi.createCustomer(customerData);
			setSelectedCustomer(response.customer);
		} catch (error) {
			console.error("Error creating customer:", error);
			// Extract meaningful error message
			let errorMessage = "Erreur lors de la création du client";
			if (error instanceof Error) {
				if (error.message.includes("already exists")) {
					errorMessage = "Un client avec cette adresse email existe déjà";
				} else {
					errorMessage = error.message;
				}
			}
			setError(errorMessage);
		}
	};

	const validateCurrentStep = (): boolean => {
		const errors: string[] = [];

		switch (currentStep) {
			case 1:
				if (!selectedCustomer) errors.push("Veuillez sélectionner un client");
				break;
			case 2:
				if (!selectedService) errors.push("Veuillez sélectionner un service");
				break;
			case 3:
				if (tierParticipants.length === 0 || tierParticipants.reduce((sum, tp) => sum + tp.count, 0) === 0) {
					errors.push("Veuillez sélectionner au moins un participant");
				}
				break;
			case 4:
				if (!selectedDate) errors.push("Veuillez sélectionner une date");
				if (!selectedTimeSlot) errors.push("Veuillez sélectionner un créneau horaire");
				break;
			case 5:
				// Service options validation
				if (serviceOptionAssignments.length > 0 && !optionsValid) {
					errors.push(...optionsValidationErrors);
				}
				break;
		}

		setValidationErrors(errors);
		return errors.length === 0;
	};

	const handleNext = () => {
		if (validateCurrentStep()) {
			setCurrentStep((prev) => prev + 1);
		}
	};

	const handlePrevious = () => {
		setCurrentStep((prev) => prev - 1);
		setValidationErrors([]);
	};

	const getTotalParticipants = useCallback(() => {
		return tierParticipants.reduce((sum, tp) => sum + tp.count, 0);
	}, [tierParticipants]);

	const totalParticipants = useMemo(() => {
		return tierParticipants.reduce((sum, tp) => sum + tp.count, 0);
	}, [tierParticipants]);

	// Memoize the validation callback to prevent infinite re-renders
	const handleOptionsValidationChange = useCallback((isValid: boolean, errors: string[]) => {
		setOptionsValid(isValid);
		setOptionsValidationErrors(errors);
	}, []);

	// Memoize the price change callback to prevent infinite re-renders
	const handleOptionsPriceChange = useCallback((price: number) => {
		setOptionsPrice(price);
	}, []);

	// Memoize the selection change callback to prevent infinite re-renders
	const handleOptionsSelectionChange = useCallback((options: OptionSelection[]) => {
		setSelectedOptions(options);
	}, []);

	const totalPrice = useMemo(() => {
		if (!selectedService) return 0;

		let basePrice = 0;
		if (selectedService.fixed_price) {
			// Fixed price regardless of participant count
			if (selectedService.pricing_tiers && selectedService.pricing_tiers.length > 0) {
				basePrice = selectedService.pricing_tiers[0].price;
			} else {
				basePrice = selectedService.base_price;
			}
		} else {
			// Variable pricing based on participants
			for (const tp of tierParticipants) {
				const tier = selectedService.pricing_tiers?.find((t) => t.id === tp.tierId);
				if (tier) {
					basePrice += tier.price * tp.count;
				} else if (tp.tierId === "default") {
					basePrice += selectedService.base_price * tp.count;
				}
			}
		}

		return basePrice + optionsPrice;
	}, [selectedService, tierParticipants, optionsPrice]);

	const handleCreateReservation = async () => {
		if (!validateCurrentStep()) return;

		setIsCreating(true);
		setError(null);

		try {
			// Prepare reservation data
			const reservationData = {
				customerId: selectedCustomer!.id,
				serviceId: selectedService!.id,
				date: selectedDate!.toISOString().split("T")[0],
				timeSlotId: selectedTimeSlot!.id,
				tierParticipants,
				selectedOptions,
				specialRequests: specialRequests.trim() || undefined,
				adminNotes: adminNotes.trim() || undefined,
			};

			const response = await fetch("/api/admin/reservations/create", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(reservationData),
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || "Erreur lors de la création de la réservation");
			}

			const result = await response.json();
			onReservationCreated(result.reservationId);
			onClose();
		} catch (error) {
			console.error("Error creating reservation:", error);
			setError(error instanceof Error ? error.message : "Erreur lors de la création de la réservation");
		} finally {
			setIsCreating(false);
		}
	};

	if (!isOpen) return null;

	const totalSteps = serviceOptionAssignments.length > 0 ? 6 : 5;
	const isLastStep = currentStep === totalSteps;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
			<div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[95vh] overflow-hidden">
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b border-gray-200">
					<div>
						<h2 className="text-2xl font-bold text-gray-900">Créer une réservation</h2>
						<p className="text-gray-600">
							Étape {currentStep} sur {totalSteps}
						</p>
					</div>
					<button onClick={onClose} className="text-gray-400 hover:text-gray-600" disabled={isCreating}>
						<X className="h-6 w-6" />
					</button>
				</div>

				{/* Progress Bar */}
				<div className="px-6 py-4 bg-gray-50">
					<div className="w-full bg-gray-200 rounded-full h-2">
						<div
							className="bg-emerald-600 h-2 rounded-full transition-all duration-300"
							style={{ width: `${(currentStep / totalSteps) * 100}%` }}
						/>
					</div>
				</div>

				{/* Content */}
				<div className={`p-6 overflow-y-auto max-h-[calc(95vh-180px)] ${currentStep === 1 ? "pb-80" : ""}`}>
					{/* Error Display */}
					{error && (
						<div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
							<div className="flex items-center">
								<AlertCircle className="h-5 w-5 text-red-600 mr-2" />
								<span className="text-red-700">{error}</span>
							</div>
						</div>
					)}

					{/* Validation Errors */}
					{validationErrors.length > 0 && (
						<div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
							<h4 className="font-medium text-red-800 mb-2">Veuillez corriger les erreurs suivantes :</h4>
							<ul className="text-sm text-red-700 space-y-1">
								{validationErrors.map((error, index) => (
									<li key={index}>• {error}</li>
								))}
							</ul>
						</div>
					)}

					{/* Step Content */}
					{currentStep === 1 && (
						<CustomerSearchSelector
							selectedCustomer={selectedCustomer}
							onCustomerSelect={setSelectedCustomer}
							onCreateNewCustomer={handleCreateNewCustomer}
							disabled={isCreating}
						/>
					)}

					{currentStep === 2 && (
						<ServiceSelector
							selectedService={selectedService}
							onServiceSelect={setSelectedService}
							disabled={isCreating}
						/>
					)}

					{currentStep === 3 && selectedService && (
						<ParticipantCountSelector
							service={selectedService}
							tierParticipants={tierParticipants}
							onTierParticipantsChange={setTierParticipants}
							disabled={isCreating}
						/>
					)}

					{currentStep === 4 && selectedService && (
						<TimeSlotSelector
							service={selectedService}
							selectedDate={selectedDate}
							selectedTimeSlot={selectedTimeSlot}
							participantCount={totalParticipants}
							onDateSelect={setSelectedDate}
							onTimeSlotSelect={setSelectedTimeSlot}
							disabled={isCreating}
						/>
					)}

					{currentStep === 5 && serviceOptionAssignments.length > 0 && (
						<div className="space-y-4">
							{loadingOptions ? (
								<div className="flex items-center justify-center py-8">
									<Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
									<span className="ml-2 text-gray-600">Chargement des options...</span>
								</div>
							) : (
								<ServiceOptionsSelector
									assignments={serviceOptionAssignments}
									selectedOptions={selectedOptions}
									onSelectionChange={handleOptionsSelectionChange}
									onPriceChange={handleOptionsPriceChange}
									onValidationChange={handleOptionsValidationChange}
									participantCount={totalParticipants}
									requiredGroups={requiredGroups}
									showValidation={true}
									showValidationErrors={false}
								/>
							)}
						</div>
					)}

					{currentStep === (serviceOptionAssignments.length > 0 ? 6 : 5) && (
						<div className="space-y-6">
							<h3 className="text-lg font-semibold text-gray-900">Finalisation</h3>

							{/* Summary */}
							<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
								<h4 className="font-medium text-gray-900 mb-3">Résumé de la réservation</h4>
								<div className="space-y-2 text-sm">
									<div className="flex justify-between">
										<span>Client:</span>
										<span className="font-medium">
											{selectedCustomer?.first_name} {selectedCustomer?.last_name}
										</span>
									</div>
									<div className="flex justify-between">
										<span>Service:</span>
										<span className="font-medium">{selectedService?.name}</span>
									</div>
									<div className="flex justify-between">
										<span>Date:</span>
										<span className="font-medium">{selectedDate?.toLocaleDateString("fr-FR")}</span>
									</div>
									<div className="flex justify-between">
										<span>Heure:</span>
										<span className="font-medium">
											{selectedTimeSlot &&
												new Date(selectedTimeSlot.start_time).toLocaleTimeString("fr-FR", {
													hour: "2-digit",
													minute: "2-digit",
													timeZone: "UTC",
												})}
										</span>
									</div>
									<div className="flex justify-between">
										<span>Participants:</span>
										<span className="font-medium">{totalParticipants}</span>
									</div>
									<div className="flex justify-between border-t pt-2 font-medium">
										<span>Total:</span>
										<span className="text-emerald-600">{totalPrice.toFixed(2)}€</span>
									</div>
								</div>
							</div>

							{/* Special Requests */}
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Demandes spéciales
								</label>
								<textarea
									value={specialRequests}
									onChange={(e) => setSpecialRequests(e.target.value)}
									rows={3}
									className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
									placeholder="Demandes particulières du client..."
									disabled={isCreating}
								/>
							</div>

							{/* Admin Notes */}
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Notes administratives
								</label>
								<textarea
									value={adminNotes}
									onChange={(e) => setAdminNotes(e.target.value)}
									rows={3}
									className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-emerald-500 focus:border-emerald-500"
									placeholder="Notes internes (non visibles par le client)..."
									disabled={isCreating}
								/>
							</div>
						</div>
					)}
				</div>

				{/* Footer */}
				<div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
					<button
						onClick={handlePrevious}
						disabled={currentStep === 1 || isCreating}
						className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
					>
						Précédent
					</button>

					<div className="flex space-x-3">
						<button
							onClick={onClose}
							disabled={isCreating}
							className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							Annuler
						</button>

						{isLastStep ? (
							<button
								onClick={handleCreateReservation}
								disabled={isCreating || !validateCurrentStep()}
								className="px-6 py-2 text-sm font-medium text-white bg-emerald-600 border border-transparent rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
							>
								{isCreating ? (
									<>
										<Loader2 className="h-4 w-4 animate-spin" />
										<span>Création...</span>
									</>
								) : (
									<>
										<CheckCircle className="h-4 w-4" />
										<span>Créer la réservation</span>
									</>
								)}
							</button>
						) : (
							<button
								onClick={handleNext}
								disabled={isCreating}
								className="px-4 py-2 text-sm font-medium text-white bg-emerald-600 border border-transparent rounded-md hover:bg-emerald-700 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								Suivant
							</button>
						)}
					</div>
				</div>
			</div>
		</div>
	);
}
