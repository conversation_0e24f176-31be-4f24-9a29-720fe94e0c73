import { supabase } from "@/lib/supabase";
import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

interface DiscountValidationRequest {
	code: string;
	serviceId?: string;
	totalAmount: number;
}

interface DiscountValidationResponse {
	valid: boolean;
	coupon?: {
		id: string;
		code: string;
		description: string | null;
		discount_type: string;
		discount_value: number;
		min_purchase_amount: number | null;
		max_discount_amount: number | null;
		usage_limit: number | null;
		current_usage: number;
		applicable_services: string[] | null;
	};
	discountAmount?: number;
	finalAmount?: number;
	error?: string;
}

// POST /api/discount-coupons/validate - Validate a discount coupon
export async function POST(request: NextRequest): Promise<NextResponse<DiscountValidationResponse>> {
	try {
		const { code, serviceId, totalAmount }: DiscountValidationRequest = await request.json();

		if (!code || !totalAmount) {
			return NextResponse.json(
				{
					valid: false,
					error: "Code et montant total requis",
				},
				{ status: 400 }
			);
		}

		// Get the discount coupon
		const { data: coupon, error: couponError } = await supabase
			.from("discount_coupons")
			.select("*")
			.eq("code", code.toUpperCase())
			.eq("is_active", true)
			.single();

		if (couponError || !coupon) {
			return NextResponse.json({
				valid: false,
				error: "Code de réduction invalide ou expiré",
			});
		}

		// Check if coupon is within valid date range
		const now = new Date();
		if (coupon.valid_from && new Date(coupon.valid_from) > now) {
			return NextResponse.json({
				valid: false,
				error: "Ce code de réduction n'est pas encore valide",
			});
		}

		if (coupon.valid_until && new Date(coupon.valid_until) < now) {
			return NextResponse.json({
				valid: false,
				error: "Ce code de réduction a expiré",
			});
		}

		// Check usage limit
		if (coupon.usage_limit && coupon.current_usage >= coupon.usage_limit) {
			return NextResponse.json({
				valid: false,
				error: "Ce code de réduction a atteint sa limite d'utilisation",
			});
		}

		// Check if coupon applies to the specific service
		if (serviceId && coupon.applicable_services && coupon.applicable_services.length > 0) {
			if (!coupon.applicable_services.includes(serviceId)) {
				return NextResponse.json({
					valid: false,
					error: "Ce code de réduction ne s'applique pas à ce service",
				});
			}
		}

		// Check minimum purchase amount
		if (coupon.min_purchase_amount && totalAmount < coupon.min_purchase_amount) {
			return NextResponse.json({
				valid: false,
				error: `Montant minimum requis: ${coupon.min_purchase_amount}€`,
			});
		}

		// Calculate discount amount
		let discountAmount = 0;
		if (coupon.discount_type === "percentage") {
			discountAmount = (totalAmount * coupon.discount_value) / 100;
		} else if (coupon.discount_type === "fixed") {
			discountAmount = coupon.discount_value;
		}

		// Apply maximum discount limit if set
		if (coupon.max_discount_amount && discountAmount > coupon.max_discount_amount) {
			discountAmount = coupon.max_discount_amount;
		}

		// Ensure discount doesn't exceed total amount
		if (discountAmount > totalAmount) {
			discountAmount = totalAmount;
		}

		const finalAmount = Math.max(0, totalAmount - discountAmount);

		return NextResponse.json({
			valid: true,
			coupon: {
				id: coupon.id,
				code: coupon.code,
				description: coupon.description,
				discount_type: coupon.discount_type,
				discount_value: coupon.discount_value,
				min_purchase_amount: coupon.min_purchase_amount,
				max_discount_amount: coupon.max_discount_amount,
				usage_limit: coupon.usage_limit,
				current_usage: coupon.current_usage,
				applicable_services: coupon.applicable_services,
			},
			discountAmount,
			finalAmount,
		});
	} catch (error) {
		console.error("Error validating discount coupon:", error);
		return NextResponse.json(
			{
				valid: false,
				error: "Erreur lors de la validation du code de réduction",
			},
			{ status: 500 }
		);
	}
}
